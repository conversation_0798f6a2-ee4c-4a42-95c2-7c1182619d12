import { NextRequest, NextResponse } from 'next/server';
import { createCheckoutSession, createStripeCustomer } from '../../../lib/stripe';
import { auth } from '@clerk/nextjs/server';

// Stripe Price IDs for ZAR plans (you'll need to create these in Stripe Dashboard)
const PRICE_IDS = {
  professional: process.env.STRIPE_PROFESSIONAL_PRICE_ID || 'price_professional_zar',
  enterprise: process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise_zar',
};

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { planId, userEmail } = await request.json();

    if (!planId || !userEmail) {
      return NextResponse.json(
        { error: 'Plan ID and email are required' },
        { status: 400 }
      );
    }

    // Skip payment for starter plan
    if (planId === 'starter') {
      return NextResponse.json({
        success: true,
        message: 'Starter plan activated'
      });
    }

    // Get the Stripe price ID for the plan
    const priceId = PRICE_IDS[planId as keyof typeof PRICE_IDS];
    
    if (!priceId) {
      return NextResponse.json(
        { error: 'Invalid plan selected' },
        { status: 400 }
      );
    }

    // Create or get Stripe customer
    const customerId = await createStripeCustomer(userEmail);

    // Create checkout session
    const session = await createCheckoutSession(
      customerId,
      priceId,
      `${process.env.NEXT_PUBLIC_APP_URL}/admin/subscription?success=true`,
      `${process.env.NEXT_PUBLIC_APP_URL}/admin/subscription?canceled=true`
    );

    return NextResponse.json({
      sessionId: session.id,
      url: session.url
    });

  } catch (error) {
    console.error('Checkout session error:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
