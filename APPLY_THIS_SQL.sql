-- COPY AND <PERSON>STE THIS INTO SUPABASE SQL EDITOR
-- Multi-Client Support Database Extensions for SaffaBot

-- Create the clients table for IT businesses to manage their customers
CREATE TABLE IF NOT EXISTS clients (
    id SERIAL PRIMARY KEY,
    it_business_user_id VARCHAR(255) NOT NULL, -- Clerk user ID of the IT business owner
    client_name VARCHAR(255) NOT NULL,
    client_email VARCHAR(255),
    client_phone VARCHAR(50),
    client_company VARCHAR(255),
    client_logo_url TEXT,
    brand_primary_color VARCHAR(7) DEFAULT '#4D7DFB', -- Hex color
    brand_secondary_color VARCHAR(7) DEFAULT '#FFCC00', -- Hex color
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Add client_id to chatbots table to associate chatbots with specific clients
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='chatbots' AND column_name='client_id') THEN
        ALTER TABLE chatbots ADD COLUMN client_id INT REFERENCES clients(id) ON DELETE CASCADE;
    END IF;
END $$;

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='chatbots' AND column_name='is_white_labeled') THEN
        ALTER TABLE chatbots ADD COLUMN is_white_labeled BOOLEAN DEFAULT false;
    END IF;
END $$;

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='chatbots' AND column_name='custom_welcome_message') THEN
        ALTER TABLE chatbots ADD COLUMN custom_welcome_message TEXT;
    END IF;
END $$;

-- Create client_branding table for advanced customization
CREATE TABLE IF NOT EXISTS client_branding (
    id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(id) ON DELETE CASCADE,
    logo_url TEXT,
    primary_color VARCHAR(7) DEFAULT '#4D7DFB',
    secondary_color VARCHAR(7) DEFAULT '#FFCC00',
    font_family VARCHAR(100) DEFAULT 'Inter',
    welcome_message TEXT,
    footer_text TEXT,
    custom_css TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create client_usage_analytics for per-client tracking
CREATE TABLE IF NOT EXISTS client_usage_analytics (
    id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(id) ON DELETE CASCADE,
    chatbot_id INT REFERENCES chatbots(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_messages INT DEFAULT 0,
    unique_users INT DEFAULT 0,
    total_sessions INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(client_id, chatbot_id, date)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_clients_it_business_user_id ON clients(it_business_user_id);
CREATE INDEX IF NOT EXISTS idx_chatbots_client_id ON chatbots(client_id);
CREATE INDEX IF NOT EXISTS idx_client_usage_analytics_client_id ON client_usage_analytics(client_id);
CREATE INDEX IF NOT EXISTS idx_client_usage_analytics_date ON client_usage_analytics(date);

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_clients_updated_at') THEN
        CREATE TRIGGER update_clients_updated_at
            BEFORE UPDATE ON clients
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Add triggers for clients table
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'set_clients_created_at') THEN
        CREATE TRIGGER set_clients_created_at
        BEFORE INSERT ON clients
        FOR EACH ROW
        EXECUTE FUNCTION set_created_at();
    END IF;
END $$;

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'set_client_branding_created_at') THEN
        CREATE TRIGGER set_client_branding_created_at
        BEFORE INSERT ON client_branding
        FOR EACH ROW
        EXECUTE FUNCTION set_created_at();
    END IF;
END $$;

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'set_client_usage_analytics_created_at') THEN
        CREATE TRIGGER set_client_usage_analytics_created_at
        BEFORE INSERT ON client_usage_analytics
        FOR EACH ROW
        EXECUTE FUNCTION set_created_at();
    END IF;
END $$;
