"use client";

import { useState } from 'react';

export default function AdvancedAnalytics({ botId }: { botId: string }) {
  const [timeframe, setTimeframe] = useState<'24h' | '7d' | '30d' | '90d'>('7d');

  return (
    <div className="space-y-8">
      {/* Time Range Selector */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Advanced Analytics</h3>
        <div className="flex gap-2">
          {['24h', '7d', '30d', '90d'].map((t) => (
            <button
              key={t}
              onClick={() => setTimeframe(t as '24h' | '7d' | '30d' | '90d')}
              className={`px-4 py-2 border rounded-lg hover:bg-gray-50 transition-colors ${
                timeframe === t ? 'bg-[#64B5F5] text-white border-[#64B5F5]' : ''
              }`}
          >
            {t}
          </button>
        ))}
      </div>

      {/* Analytics Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Conversation Trends */}
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-semibold mb-4">Conversation Trends</h4>
          <div className="h-48 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center text-gray-500">
              <p className="text-sm">Chart visualization coming soon</p>
              <p className="text-xs">Track conversation patterns over time</p>
            </div>
          </div>
        </div>

        {/* User Engagement */}
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-semibold mb-4">User Engagement</h4>
          <div className="h-48 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center text-gray-500">
              <p className="text-sm">Engagement metrics coming soon</p>
              <p className="text-xs">Monitor user interaction patterns</p>
            </div>
          </div>
        </div>

        {/* Response Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-semibold mb-4">Response Performance</h4>
          <div className="h-48 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center text-gray-500">
              <p className="text-sm">Performance analytics coming soon</p>
              <p className="text-xs">Track response times and accuracy</p>
            </div>
          </div>
        </div>

        {/* Popular Topics */}
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-semibold mb-4">Popular Topics</h4>
          <div className="h-48 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center text-gray-500">
              <p className="text-sm">Topic analysis coming soon</p>
              <p className="text-xs">Identify trending conversation topics</p>
            </div>
          </div>
        </div>
      </div>

      {/* Future Features Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-blue-800 mb-2">🚀 Coming Soon</h4>
        <p className="text-blue-700 mb-3">
          Advanced analytics features are in development and will include:
        </p>
        <ul className="text-blue-600 text-sm space-y-1">
          <li>• Real-time conversation analytics and charts</li>
          <li>• User behavior tracking and retention metrics</li>
          <li>• AI performance insights and optimization suggestions</li>
          <li>• Custom reporting and data export capabilities</li>
          <li>• Integration with popular analytics platforms</li>
        </ul>
      </div>
    </div>
  );
}