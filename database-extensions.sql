-- Multi-Client Support Database Extensions for SaffaBot

-- Create the clients table for IT businesses to manage their customers
CREATE TABLE clients (
    id SERIAL PRIMARY KEY,
    it_business_user_id VARCHAR(255) NOT NULL, -- Clerk user ID of the IT business owner
    client_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    client_email VARCHAR(255),
    client_phone VARCHAR(50),
    client_company VARCHAR(255),
    client_logo_url TEXT,
    brand_primary_color VARCHAR(7) DEFAULT '#4D7DFB', -- Hex color
    brand_secondary_color VARCHAR(7) DEFAULT '#FFCC00', -- Hex color
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Add client_id to chatbots table to associate chatbots with specific clients
ALTER TABLE chatbots ADD COLUMN client_id INT REFERENCES clients(id) ON DELETE CASCADE;
ALTER TABLE chatbots ADD COLUMN is_white_labeled BOOLEAN DEFAULT false;
ALTER TABLE chatbots ADD COLUMN custom_welcome_message TEXT;

-- Create client_branding table for advanced customization
CREATE TABLE client_branding (
    id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(id) ON DELETE CASCADE,
    logo_url TEXT,
    primary_color VARCHAR(7) DEFAULT '#4D7DFB',
    secondary_color VARCHAR(7) DEFAULT '#FFCC00',
    font_family VARCHAR(100) DEFAULT 'Inter',
    welcome_message TEXT,
    footer_text TEXT,
    custom_css TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create client_usage_analytics for per-client tracking
CREATE TABLE client_usage_analytics (
    id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(id) ON DELETE CASCADE,
    chatbot_id INT REFERENCES chatbots(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_messages INT DEFAULT 0,
    unique_users INT DEFAULT 0,
    total_sessions INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(client_id, chatbot_id, date)
);

-- Create indexes for better performance
CREATE INDEX idx_clients_it_business_user_id ON clients(it_business_user_id);
CREATE INDEX idx_chatbots_client_id ON chatbots(client_id);
CREATE INDEX idx_client_usage_analytics_client_id ON client_usage_analytics(client_id);
CREATE INDEX idx_client_usage_analytics_date ON client_usage_analytics(date);

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_clients_updated_at
    BEFORE UPDATE ON clients
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO clients (it_business_user_id, client_name, client_email, client_company) VALUES
('clerk_user_1', 'ABC Corp', '<EMAIL>', 'ABC Corporation'),
('clerk_user_1', 'XYZ Ltd', '<EMAIL>', 'XYZ Limited'),
('clerk_user_2', 'Tech Solutions', '<EMAIL>', 'Tech Solutions Inc');

-- Update existing chatbots to have client associations (optional)
-- UPDATE chatbots SET client_id = 1 WHERE id = 1;
-- UPDATE chatbots SET client_id = 2 WHERE id = 2;
