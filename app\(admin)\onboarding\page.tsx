'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../supabaseClient';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { CheckCircle, Circle, ArrowRight, Building, Bot, Palette, Share } from 'lucide-react';

interface OnboardingStep {
  id: number;
  title: string;
  description: string;
  completed: boolean;
}

interface ClientData {
  client_name: string;
  client_email: string;
  client_company: string;
  client_phone: string;
  brand_primary_color: string;
  brand_secondary_color: string;
}

interface ChatbotData {
  name: string;
  welcome_message: string;
  characteristics: string[];
}

export default function OnboardingPage() {
  const { user } = useUser();
  const router = useRouter();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  
  // Client data
  const [clientData, setClientData] = useState<ClientData>({
    client_name: '',
    client_email: '',
    client_company: '',
    client_phone: '',
    brand_primary_color: '#4D7DFB',
    brand_secondary_color: '#FFCC00'
  });

  // Chatbot data
  const [chatbotData, setChatbotData] = useState<ChatbotData>({
    name: '',
    welcome_message: 'Hello! How can I help you today?',
    characteristics: ['I am a helpful IT support assistant.']
  });

  const [createdClientId, setCreatedClientId] = useState<number | null>(null);
  const [createdChatbotId, setCreatedChatbotId] = useState<number | null>(null);

  const steps: OnboardingStep[] = [
    {
      id: 1,
      title: 'Client Information',
      description: 'Add your client\'s basic information',
      completed: currentStep > 1
    },
    {
      id: 2,
      title: 'Brand Customization',
      description: 'Set up client branding and colors',
      completed: currentStep > 2
    },
    {
      id: 3,
      title: 'Create Chatbot',
      description: 'Configure the AI chatbot for this client',
      completed: currentStep > 3
    },
    {
      id: 4,
      title: 'Ready to Deploy',
      description: 'Your client setup is complete!',
      completed: currentStep > 4
    }
  ];

  const handleStep1Submit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('clients')
        .insert([{
          it_business_user_id: user.id,
          ...clientData
        }])
        .select()
        .single();

      if (error) throw error;
      
      setCreatedClientId(data.id);
      setCurrentStep(2);
    } catch (error) {
      console.error('Error creating client:', error);
      alert('Failed to create client. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStep2Submit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!createdClientId) return;

    setLoading(true);
    try {
      // Update client with branding
      const { error: clientError } = await supabase
        .from('clients')
        .update({
          brand_primary_color: clientData.brand_primary_color,
          brand_secondary_color: clientData.brand_secondary_color
        })
        .eq('id', createdClientId);

      if (clientError) throw clientError;

      // Create branding record
      const { error: brandingError } = await supabase
        .from('client_branding')
        .insert([{
          client_id: createdClientId,
          primary_color: clientData.brand_primary_color,
          secondary_color: clientData.brand_secondary_color,
          font_family: 'Inter',
          welcome_message: chatbotData.welcome_message,
          footer_text: `Powered by ${clientData.client_company}`
        }]);

      if (brandingError) throw brandingError;
      
      setCurrentStep(3);
    } catch (error) {
      console.error('Error saving branding:', error);
      alert('Failed to save branding. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStep3Submit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !createdClientId) return;

    setLoading(true);
    try {
      // Create chatbot
      const { data: chatbotResult, error: chatbotError } = await supabase
        .from('chatbots')
        .insert([{
          clerk_user_id: user.id,
          client_id: createdClientId,
          name: chatbotData.name,
          is_white_labeled: true,
          custom_welcome_message: chatbotData.welcome_message
        }])
        .select()
        .single();

      if (chatbotError) throw chatbotError;
      
      setCreatedChatbotId(chatbotResult.id);

      // Add characteristics
      const characteristicsToInsert = chatbotData.characteristics.map(content => ({
        chatbot_id: chatbotResult.id,
        content
      }));

      const { error: characteristicsError } = await supabase
        .from('chatbot_characteristics')
        .insert(characteristicsToInsert);

      if (characteristicsError) throw characteristicsError;
      
      setCurrentStep(4);
    } catch (error) {
      console.error('Error creating chatbot:', error);
      alert('Failed to create chatbot. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const addCharacteristic = () => {
    setChatbotData({
      ...chatbotData,
      characteristics: [...chatbotData.characteristics, '']
    });
  };

  const updateCharacteristic = (index: number, value: string) => {
    const newCharacteristics = [...chatbotData.characteristics];
    newCharacteristics[index] = value;
    setChatbotData({
      ...chatbotData,
      characteristics: newCharacteristics
    });
  };

  const removeCharacteristic = (index: number) => {
    setChatbotData({
      ...chatbotData,
      characteristics: chatbotData.characteristics.filter((_, i) => i !== index)
    });
  };

  return (
    <div className="flex-1 p-10">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Quick Client Setup
          </h1>
          <p className="text-gray-600">
            Get your new client up and running in just a few steps
          </p>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-8">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className="flex flex-col items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  step.completed 
                    ? 'bg-green-500 text-white' 
                    : currentStep === step.id 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-500'
                }`}>
                  {step.completed ? (
                    <CheckCircle className="h-6 w-6" />
                  ) : (
                    <span>{step.id}</span>
                  )}
                </div>
                <div className="text-center mt-2">
                  <p className="text-sm font-medium">{step.title}</p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-16 h-1 mx-4 ${
                  step.completed ? 'bg-green-500' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-lg shadow p-8">
          {/* Step 1: Client Information */}
          {currentStep === 1 && (
            <form onSubmit={handleStep1Submit} className="space-y-6">
              <div className="flex items-center mb-6">
                <Building className="h-6 w-6 text-blue-600 mr-3" />
                <h2 className="text-2xl font-bold">Client Information</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  placeholder="Client Name"
                  value={clientData.client_name}
                  onChange={(e) => setClientData({...clientData, client_name: e.target.value})}
                  required
                />
                <Input
                  placeholder="Company Name"
                  value={clientData.client_company}
                  onChange={(e) => setClientData({...clientData, client_company: e.target.value})}
                  required
                />
                <Input
                  type="email"
                  placeholder="Email Address"
                  value={clientData.client_email}
                  onChange={(e) => setClientData({...clientData, client_email: e.target.value})}
                  required
                />
                <Input
                  placeholder="Phone Number"
                  value={clientData.client_phone}
                  onChange={(e) => setClientData({...clientData, client_phone: e.target.value})}
                />
              </div>

              <div className="flex justify-end">
                <Button type="submit" disabled={loading}>
                  {loading ? 'Creating...' : 'Next Step'}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </form>
          )}

          {/* Step 2: Brand Customization */}
          {currentStep === 2 && (
            <form onSubmit={handleStep2Submit} className="space-y-6">
              <div className="flex items-center mb-6">
                <Palette className="h-6 w-6 text-blue-600 mr-3" />
                <h2 className="text-2xl font-bold">Brand Customization</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Primary Brand Color
                  </label>
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      value={clientData.brand_primary_color}
                      onChange={(e) => setClientData({...clientData, brand_primary_color: e.target.value})}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <Input
                      value={clientData.brand_primary_color}
                      onChange={(e) => setClientData({...clientData, brand_primary_color: e.target.value})}
                      placeholder="#4D7DFB"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Secondary Brand Color
                  </label>
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      value={clientData.brand_secondary_color}
                      onChange={(e) => setClientData({...clientData, brand_secondary_color: e.target.value})}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <Input
                      value={clientData.brand_secondary_color}
                      onChange={(e) => setClientData({...clientData, brand_secondary_color: e.target.value})}
                      placeholder="#FFCC00"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Welcome Message
                </label>
                <Input
                  value={chatbotData.welcome_message}
                  onChange={(e) => setChatbotData({...chatbotData, welcome_message: e.target.value})}
                  placeholder="Hello! How can I help you today?"
                />
              </div>

              {/* Preview */}
              <div className="border rounded-lg p-4 bg-gray-50">
                <h3 className="font-medium mb-3">Preview</h3>
                <div className="border rounded-lg overflow-hidden bg-white max-w-sm">
                  <div 
                    className="p-3 text-white"
                    style={{ backgroundColor: clientData.brand_primary_color }}
                  >
                    <h4 className="font-bold">{clientData.client_company} Support</h4>
                  </div>
                  <div className="p-3">
                    <div 
                      className="inline-block px-3 py-2 rounded-lg text-white text-sm"
                      style={{ backgroundColor: clientData.brand_primary_color }}
                    >
                      {chatbotData.welcome_message}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setCurrentStep(1)}
                >
                  Back
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Saving...' : 'Next Step'}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </form>
          )}

          {/* Step 3: Create Chatbot */}
          {currentStep === 3 && (
            <form onSubmit={handleStep3Submit} className="space-y-6">
              <div className="flex items-center mb-6">
                <Bot className="h-6 w-6 text-blue-600 mr-3" />
                <h2 className="text-2xl font-bold">Create Chatbot</h2>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Chatbot Name
                </label>
                <Input
                  value={chatbotData.name}
                  onChange={(e) => setChatbotData({...chatbotData, name: e.target.value})}
                  placeholder={`${clientData.client_company} Support Bot`}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  AI Instructions (What the chatbot knows)
                </label>
                {chatbotData.characteristics.map((characteristic, index) => (
                  <div key={index} className="flex items-center space-x-2 mb-2">
                    <Input
                      value={characteristic}
                      onChange={(e) => updateCharacteristic(index, e.target.value)}
                      placeholder="e.g., I can help with password resets and account issues"
                      className="flex-1"
                    />
                    {chatbotData.characteristics.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeCharacteristic(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={addCharacteristic}
                  className="mt-2"
                >
                  Add Instruction
                </Button>
              </div>

              <div className="flex justify-between">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setCurrentStep(2)}
                >
                  Back
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Creating...' : 'Create Chatbot'}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </form>
          )}

          {/* Step 4: Complete */}
          {currentStep === 4 && (
            <div className="text-center space-y-6">
              <div className="flex items-center justify-center mb-6">
                <CheckCircle className="h-16 w-16 text-green-500" />
              </div>
              
              <h2 className="text-2xl font-bold text-gray-900">
                🎉 Client Setup Complete!
              </h2>
              
              <p className="text-gray-600 max-w-md mx-auto">
                {clientData.client_company} is now ready to use their custom AI chatbot. 
                You can share the chatbot link with them or continue customizing.
              </p>

              <div className="bg-gray-50 rounded-lg p-6 max-w-md mx-auto">
                <h3 className="font-medium mb-3">What's Next?</h3>
                <ul className="text-sm text-gray-600 space-y-2 text-left">
                  <li>• Share the chatbot link with your client</li>
                  <li>• Test the chatbot functionality</li>
                  <li>• Monitor usage in analytics</li>
                  <li>• Customize further if needed</li>
                </ul>
              </div>

              <div className="flex justify-center space-x-4">
                <Button onClick={() => router.push('/clients')}>
                  <Building className="h-4 w-4 mr-2" />
                  View All Clients
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => router.push(`/chatbot/${createdChatbotId}`)}
                >
                  <Share className="h-4 w-4 mr-2" />
                  Test Chatbot
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => {
                    setCurrentStep(1);
                    setClientData({
                      client_name: '',
                      client_email: '',
                      client_company: '',
                      client_phone: '',
                      brand_primary_color: '#4D7DFB',
                      brand_secondary_color: '#FFCC00'
                    });
                    setChatbotData({
                      name: '',
                      welcome_message: 'Hello! How can I help you today?',
                      characteristics: ['I am a helpful IT support assistant.']
                    });
                    setCreatedClientId(null);
                    setCreatedChatbotId(null);
                  }}
                >
                  Add Another Client
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
