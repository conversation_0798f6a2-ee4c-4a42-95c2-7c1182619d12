// "use client";

// import { useState, useEffect } from 'react';
// import { SketchPicker } from 'react-color';
// import { supabase } from '../supabaseClient';

// interface CustomizationSettings {
//   themeColor: string;
//   fontFamily: string;
//   chatBubbleStyle: {
//     borderRadius: string;
//     padding: string;
//     shadow: string;
//   };
//   customCSS: string;
//   layout: 'floating' | 'embedded';
//   position: 'left' | 'right';
//   welcomeMessage: string;
// }

// export default function BotCustomizer({ botId }: { botId: string }) {
//   const [settings, setSettings] = useState<CustomizationSettings>({
//     themeColor: '#0088cc',
//     fontFamily: 'Inter',
//     chatBubbleStyle: {
//       borderRadius: '10px',
//       padding: '10px',
//       shadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
//     },
//     customCSS: '',
//     layout: 'floating',
//     position: 'left',
//     welcomeMessage: 'Welcome to our chat!',
//   });

//   useEffect(() => {
//     const fetchSettings = async () => {
//       const { data, error } = await supabase
//         .from('bot_customization')
//         .select('*')
//         .eq('bot_id', botId);

//       if (error) {
//         console.error('Error fetching bot customization:', error);
//       } else if (data) {
//         setSettings(data[0]);
//       }
//     };

//     fetchSettings();
//   }, [botId]);

//   const handleThemeColorChange = (color: any) => {
//     setSettings((prevSettings) => ({
//       ...prevSettings,
//       themeColor: color.hex,
//     }));
//   };

//   const handleFontFamilyChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
//     setSettings((prevSettings) => ({
//       ...prevSettings,
//       fontFamily: event.target.value,
//     }));
//   };

//   const handleChatBubbleStyleChange = (key: keyof typeof settings.chatBubbleStyle) => (value: string) => {
//     setSettings((prevSettings) => ({
//       ...prevSettings,
//       chatBubbleStyle: {
//         ...prevSettings.chatBubbleStyle,
//         [key]: value,
//       },
//     }));
//   };

//   const handleCustomCSSChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
//     setSettings((prevSettings) => ({
//       ...prevSettings,
//       customCSS: event.target.value,
//     }));
//   };

//   const handleLayoutChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
//     setSettings((prevSettings) => ({
//       ...prevSettings,
//       layout: event.target.value as 'floating' | 'embedded',
//     }));
//   };

//   const handlePositionChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
//     setSettings((prevSettings) => ({
//       ...prevSettings,
//       position: event.target.value as 'left' | 'right',
//     }));
//   };

//   const handleWelcomeMessageChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
//     setSettings((prevSettings) => ({
//       ...prevSettings,
//       welcomeMessage: event.target.value,
//     }));
//   };

//   const saveSettings = async () => {
//     const { data, error } = await supabase
//       .from('bot_customization')
//       .upsert([{ bot_id: botId, ...settings }]);

//     if (error) {
//       console.error('Error saving bot customization:', error);
//     } else {
//       console.log('Bot customization saved successfully');
//     }
//   };

//   return (
//     <div className="p-4">
//       <h1 className="text-2xl font-bold mb-4">Bot Customization</h1>

//       <div className="mb-4">
//         <label className="block text-sm font-medium text-gray-700">Theme Color</label>
//         <SketchPicker
//           color={settings.themeColor}
//           onChange={handleThemeColorChange}
//           className="mt-1"
//         />
//       </div>

//       <div className="mb-4">
//         <label className="block text-sm font-medium text-gray-700">Font Family</label>
//         <select
//           value={settings.fontFamily}
//           onChange={handleFontFamilyChange}
//           className="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//         >
//           <option value="Inter">Inter</option>
//           <option value="Arial">Arial</option>
//           <option value="Helvetica">Helvetica</option>
//           <option value="Times New Roman">Times New Roman</option>
//           <option value="Verdana">Verdana</option>
//         </select>
//       </div>

//       <div className="mb-4">
//         <label className="block text-sm font-medium text-gray-700">Chat Bubble Style</label>
//         <div className="mt-1">
//           <label className="block text-sm font-medium text-gray-700">Border Radius</label>
//           <input
//             type="text"
//             value={settings.chatBubbleStyle.borderRadius}
//             onChange={(e) => handleChatBubbleStyleChange('borderRadius')(e.target.value)}
//             className="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//           />
//         </div>
//         <div className="mt-1">
//           <label className="block text-sm font-medium text-gray-700">Padding</label>
//           <input
//             type="text"
//             value={settings.chatBubbleStyle.padding}
//             onChange={(e) => handleChatBubbleStyleChange('padding')(e.target.value)}
//             className="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//           />
//         </div>
//         <div className="mt-1">
//           <label className="block text-sm font-medium text-gray-700">Shadow</label>
//           <input
//             type="text"
//             value={settings.chatBubbleStyle.shadow}
//             onChange={(e) => handleChatBubbleStyleChange('shadow')(e.target.value)}
//             className="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//           />
//         </div>
//       </div>

//       <div className="mb-4">
//         <label className="block text-sm font-medium text-gray-700">Custom CSS</label>
//         <textarea
//           value={settings.customCSS}
//           onChange={handleCustomCSSChange}
//           className="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//           rows={4}
//         />
//       </div>

//       <div className="mb-4">
//         <label className="block text-sm font-medium text-gray-700">Layout</label>
//         <select
//           value={settings.layout}
//           onChange={handleLayoutChange}
//           className="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//         >
//           <option value="floating">Floating</option>
//           <option value="embedded">Embedded</option>
//         </select>
//       </div>

//       <div className="mb-4">
//         <label className="block text-sm font-medium text-gray-700">Position</label>
//         <select
//           value={settings.position}
//           onChange={handlePositionChange}
//           className="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//         >
//           <option value="left">Left</option>
//           <option value="right">Right</option>
//         </select>
//       </div>

//       <div className="mb-4">
//         <label className="block text-sm font-medium text-gray-700">Welcome Message</label>
//         <textarea
//           value={settings.welcomeMessage}
//           onChange={handleWelcomeMessageChange}
//           className="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//           rows={4}
//         />
//       </div>

//       <button
//         onClick={saveSettings}
//         className="mt-4 w-full rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
//       >
//         Save Settings
//       </button>
//     </div>
//   );
// } 