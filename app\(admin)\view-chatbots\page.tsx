import Avatar from "../../../components/Avatar";
import { Button } from "../../../components/ui/button";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import Link from "next/link";
import { auth } from "@clerk/nextjs/server";
import BotPerformanceMetrics from "../../../components/BotPerformanceMetrics";

// Types
interface ChatSession {
  id: string;
  messages?: { id: string; sender: string; content: string }[];
}

interface Characteristic {
  id: string;
  content: string;
}

interface Chatbot {
  id: string;
  name: string;
  created_at: string;
  chatbot_characteristics: Characteristic[];
  chat_sessions: ChatSession[];
}

export const dynamic = "force-dynamic";

async function ViewChatbots() {
  const { userId } = await auth();
  if (!userId) {
    return (
      <div className="text-center">
        <p>You must be logged in to view your chatbots.</p>
        {/* Optionally, add a login redirect or button */}
      </div>
    );
  }

  // Initialize Supabase client
  const cookieStore = await cookies();
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  // State to manage loading and error states
  let chatbots: Chatbot[] = [];
  let loading = true;
  let errorMessage: string | null = null;

  try {
    // Fetch chatbots with characteristics and chat sessions
    const { data, error } = await supabase
      .from('chatbots')
      .select(`
        id,
        name,
        created_at,
        chatbot_characteristics ( id, content ),
        chat_sessions (
          id,
          messages ( id, sender, content )
        )
      `)
      .eq('clerk_user_id', userId);

    if (error) {
      throw new Error(error.message || 'Failed to fetch chatbots');
    }

    chatbots = [...(data || [])].sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  } catch (err) {
    errorMessage = err instanceof Error ? err.message : 'Unexpected error';
  } finally {
    loading = false;
  }

  // Show loading spinner while fetching data
  if (loading) {
    return <div>Loading your chatbots...</div>;
  }

  // Handle error state
  if (errorMessage) {
    return <div>Error loading chatbots: {errorMessage}</div>;
  }

  return (
    <div className="flex-1 pb-20 p-10">
      <h1 className="text-xl lg:text-3xl font-semibold mb-5">Active Chatbots</h1>

      {chatbots.length === 0 ? (
        <div>
          <p>You have not created any chatbots yet. Click on the button below to create one.</p>
          <Link href="/create-chatbot">
            <Button className="bg-[#64B5F5] text-white p-3 rounded-md mt-5">
              Create Chatbot
            </Button>
          </Link>
        </div>
      ) : (
        <div className="space-y-8">
          {chatbots.map((chatbot) => (
            <div key={chatbot.id} className="bg-white border rounded-lg shadow-sm hover:shadow-md transition-shadow">
              {/* Chatbot Header */}
              <Link href={`/edit-chatbot/${chatbot.id}`}>
                <div className="p-6 border-b hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-4">
                      <Avatar seed={chatbot.name} />
                      <div>
                        <h2 className="text-xl font-bold">{chatbot.name}</h2>
                        <p className="text-sm text-gray-500">
                          Created: {new Date(chatbot.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Sessions</p>
                      <p className="text-2xl font-bold text-[#64B5F5]">
                        {chatbot.chat_sessions?.length || 0}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>

              {/* Chatbot Details */}
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Characteristics */}
                  <div>
                    <h3 className="font-semibold text-gray-700 mb-3">Characteristics</h3>
                    {chatbot.chatbot_characteristics?.length === 0 ? (
                      <p className="text-gray-500 text-sm">No characteristics added yet.</p>
                    ) : (
                      <ul className="space-y-1">
                        {chatbot.chatbot_characteristics?.slice(0, 3).map((characteristic) => (
                          <li
                            className="text-sm text-gray-600 flex items-start"
                            key={characteristic.id}
                          >
                            <span className="w-2 h-2 bg-[#64B5F5] rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            <span className="break-words">{characteristic.content}</span>
                          </li>
                        ))}
                        {chatbot.chatbot_characteristics?.length > 3 && (
                          <li className="text-sm text-gray-400">
                            +{chatbot.chatbot_characteristics.length - 3} more...
                          </li>
                        )}
                      </ul>
                    )}
                  </div>

                  {/* Performance Metrics */}
                  <div>
                    <h3 className="font-semibold text-gray-700 mb-3">Performance Metrics</h3>
                    <BotPerformanceMetrics botId={chatbot.id.toString()} />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 mt-6 pt-4 border-t">
                  <Link href={`/edit-chatbot/${chatbot.id}`}>
                    <Button className="bg-[#64B5F5] hover:bg-[#42A5F5] text-white">
                      Edit Chatbot
                    </Button>
                  </Link>
                  <Link href={`/review-sessions?bot=${chatbot.id}`}>
                    <Button variant="outline" className="border-[#64B5F5] text-[#64B5F5] hover:bg-[#64B5F5] hover:text-white">
                      View Sessions
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default ViewChatbots;