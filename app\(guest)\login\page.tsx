"use client"; // Mark the component as a client component

import { useEffect, useState } from "react";
import Avatar from "../../../components/Avatar";
import { SignIn, useClerk } from "@clerk/nextjs";
import { useRouter } from "next/navigation"; // Import useRouter

function LoginPage() {
  const [isClient, setIsClient] = useState(false);
  const clerk = useClerk();
  const router = useRouter(); // Initialize useRouter

  // Ensure that useClerk is only used on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Redirect if user is already signed in
  useEffect(() => {
    if (isClient && clerk.user?.id) {
      router.push("/"); // Use router.push for programmatic redirection
    }
  }, [isClient, clerk.user?.id, router]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-[#64B5F5]">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        {/* Left side - Avatar and Text */}
        <div className="flex flex-col items-center justify-center space-y-5 text-white">
          <div className="rounded-full bg-white p-5">
            <Avatar seed="Support Agent" className="h-60 w-60" />
          </div>
          <div className="text-center">
            <h1 className="text-4xl">Saffabot</h1>
            <h2 className="text-base font-light">Your Customisable AI Chat BOT</h2>
            <h3 className="my-5 font-bold">Sign in to get started</h3>
          </div>
        </div>

        {/* Right side - Sign In form */}
        <div className="flex items-center justify-center">
          <SignIn routing="hash" fallbackRedirectUrl="/" />
        </div>
      </div>
    </div>
  );
}

export default LoginPage;
