import { supabase } from '../../supabaseClient'; // Assuming you have this setup
// import { Chatbot } from '../../types/types'; // Ensure correct import path
// Type Definitions for input and return types

interface Chatbot {
  id: number;
  name: string;
  created_at: string;
  chatbot_characteristics: { id: number; content: string; created_at: string }[];
  chat_sessions: {
    id: number;
    created_at: string;
    guest_id: number;
    messages: {
      id: number;
      content: string;
      created_at: string;
    }[];
  }[];
}

// Utility function for error handling
const handleError = (error: any) => {
  console.error('Query error:', error);
  throw new Error(error?.message || 'An error occurred while fetching data.');
};

// Fetch Chatbot by ID
export const getChatbotById = async (id: number): Promise<Chatbot | null> => {
  try {
    const { data, error } = await supabase
      .from('chatbots')
      .select(`
        id,
        name,
        created_at,
        chatbot_characteristics (id, content, created_at),
        chat_sessions (
          id,
          created_at,
          guest_id,
          messages (id, content, created_at)
        )
      `)
      .eq('id', id)
      .single();

    if (error) handleError(error);
    return data;
  } catch (error) {
    handleError(error);
    return null;
  }
};

// Fetch Chatbots by Clerk User ID
export const getChatbotsByUser = async (clerk_user_id: string): Promise<Chatbot[] | null> => {
  try {
    const { data, error } = await supabase
      .from('chatbots')
      .select(`
        id,
        name,
        created_at,
        chatbot_characteristics (id, content, created_at),
        chat_sessions (
          id,
          created_at,
          guest_id,
          messages (id, content, created_at)
        )
      `)
      .eq('clerk_user_id', clerk_user_id);

    if (error) handleError(error);
    return data;
  } catch (error) {
    handleError(error);
    return null;
  }
};

// Fetch User's Chatbots and Sessions (with basic guest info)
export const getUserChatbots = async (userId: string): Promise<Chatbot[] | null> => {
  try {
    const { data, error } = await supabase
      .from('chatbots')
      .select(`
        id,
        name,
        created_at,
        clerk_user_id,
        status,
        chatbot_characteristics (
          id,
          chatbot_id,
          content,
          created_at
        ),
        chat_sessions (
          id,
          created_at,
          name,
          email,
          guest_id,
          status,
          guests (
            id,
            name,
            email,
            created_at
          )
        )
      `)
      .eq('clerk_user_id', userId);

    if (error) throw error;
    return data as unknown as Chatbot[];
  } catch (error) {
    console.error('Error fetching user chatbots:', error);
    throw error;
  }
};

// Fetch Chat Session Messages by Session ID
export const getChatSessionMessages = async (id: string) => {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .select(`
        id,
        created_at,
        name,
        email,
        guest_id,
        status,
        chatbot_id,
        messages (
          id,
          content,
          role,
          created_at
        ),
        chatbots!chat_sessions_chatbot_id_fkey (
          name,
          id
        ),
        guest:guests (
          id,
          name,
          email,
          created_at
        )
      `)
      .eq('id', id)
      .single();

    if (error) handleError(error);
    return data;
  } catch (error) {
    handleError(error);
    return null;
  }
};

// Fetch Messages by Chat Session ID
export const getMessagesByChatSessionId = async (chat_session_id: number) => {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .select(`
        id,
        messages (id, content, sender, created_at)
      `)
      .eq('id', chat_session_id)
      .single();

    if (error) handleError(error);
    return data;
  } catch (error) {
    handleError(error);
    return null;
  }
};
// Assuming GraphQL for fetching chatbots by user
export const fetchChatbots = async (userId: string) => {
  const query = `
    query getChatbotsByUser($userId: String!) {
      chatbots(where: { clerk_user_id: { _eq: $userId } }) {
        id
        name
        status
        created_at
        chat_sessions {
          id
          created_at
          guest_id
          status
          guest {
            id
            name
            email
            created_at
          }
        }
      }
    }
  `;

  const variables = { userId };

  try {
    const response = await fetch('/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, variables }),
    });

    const data = await response.json();

    if (data.errors) {
      throw new Error('Error fetching chatbots');
    }

    return data.data.chatbots;
  } catch (error) {
    console.error('Error fetching chatbots:', error);
    throw error;
  }
};
// Function to fetch chat sessions for a user
export const fetchChatSessions = async (userId: string) => {
  const { data, error } = await supabase
    .from('chat_sessions')
    .select(`
      id,
      created_at,
      name,
      email,
      guest_id,
      status,
      chatbot_id,
      messages (
        id,
        content,
        role,
        created_at
      ),
      chatbots!chat_sessions_chatbot_id_fkey (
        name,
        id
      ),
      guest:guests (
        id,
        name,
        email,
        created_at
      )
    `)
    .eq('clerk_user_id', userId); // Assuming you use 'clerk_user_id' for user identification

  if (error) {
    throw new Error(`Error fetching chat sessions: ${error.message}`);
  }
  return data;
};