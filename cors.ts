// middleware/cors.ts
import Cors from 'cors';
import type { NextApiRequest, NextApiResponse } from 'next';

const cors = Cors({
  methods: ['GET', 'HEAD', 'POST', 'OPTIONS'],
  origin: process.env.NODE_ENV === 'development'
    ? 'http://localhost:3000'
    : 'https://saffabot-ai.co.za/login', // Use a string for the origin
  credentials: true,
});

// Helper method to run middleware
export function runMiddleware(req: NextApiRequest, res: NextApiResponse) {
  return new Promise<void>((resolve, reject) => {
    cors(req, res, (result) => {
      if (result instanceof Error) {
     
        return reject(result);
      }
      resolve();
    });
  });
}

export default cors;
