'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { supabase } from '../../../supabaseClient';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Plus, Building, Users, Bot, MoreVertical, Edit, Trash2, Palette, Zap, BarChart3 } from 'lucide-react';
import Link from 'next/link';

interface Client {
  id: number;
  client_name: string;
  client_email: string;
  client_company: string;
  client_phone?: string;
  brand_primary_color: string;
  brand_secondary_color: string;
  is_active: boolean;
  created_at: string;
  chatbot_count?: number;
}

export default function ClientsPage() {
  const { user } = useUser();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newClient, setNewClient] = useState({
    client_name: '',
    client_email: '',
    client_company: '',
    client_phone: '',
    brand_primary_color: '#4D7DFB',
    brand_secondary_color: '#FFCC00'
  });

  useEffect(() => {
    if (user) {
      fetchClients();
    }
  }, [user]);

  const fetchClients = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('clients')
        .select(`
          *,
          chatbots(count)
        `)
        .eq('it_business_user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Process the data to include chatbot count
      const processedClients = data?.map(client => ({
        ...client,
        chatbot_count: client.chatbots?.length || 0
      })) || [];

      setClients(processedClients);
    } catch (error) {
      console.error('Error fetching clients:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddClient = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    try {
      console.log('User ID:', user.id);
      console.log('Client data:', newClient);

      const { data, error } = await supabase
        .from('clients')
        .insert([{
          it_business_user_id: user.id,
          ...newClient
        }])
        .select()
        .single();

      if (error) throw error;

      setClients([data, ...clients]);
      setNewClient({
        client_name: '',
        client_email: '',
        client_company: '',
        client_phone: '',
        brand_primary_color: '#4D7DFB',
        brand_secondary_color: '#FFCC00'
      });
      setShowAddForm(false);
    } catch (error) {
      console.error('Error adding client:', error);
      // Show more detailed error information
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`Failed to add client: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClient = async (clientId: number) => {
    if (!confirm('Are you sure you want to delete this client? This will also delete all associated chatbots.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', clientId);

      if (error) throw error;

      setClients(clients.filter(client => client.id !== clientId));
    } catch (error) {
      console.error('Error deleting client:', error);
      alert('Failed to delete client. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex-1 p-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin h-8 w-8 border-b-2 border-blue-600 rounded-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-10">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Client Management</h1>
            <p className="text-gray-600 mt-2">
              Manage your IT support clients and their chatbots
            </p>
          </div>
          <div className="flex space-x-3">
            <Link href="/onboarding">
              <Button className="bg-green-600 hover:bg-green-700">
                <Zap className="h-4 w-4 mr-2" />
                Quick Setup
              </Button>
            </Link>
            <Button
              onClick={() => setShowAddForm(true)}
              variant="outline"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Client
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Building className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Clients</p>
                <p className="text-2xl font-bold text-gray-900">{clients.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Bot className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Chatbots</p>
                <p className="text-2xl font-bold text-gray-900">
                  {clients.reduce((sum, client) => sum + (client.chatbot_count || 0), 0)}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Clients</p>
                <p className="text-2xl font-bold text-gray-900">
                  {clients.filter(client => client.is_active).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Add Client Form */}
        {showAddForm && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h2 className="text-xl font-bold mb-4">Add New Client</h2>
            <form onSubmit={handleAddClient} className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder="Client Name"
                value={newClient.client_name}
                onChange={(e) => setNewClient({...newClient, client_name: e.target.value})}
                required
              />
              <Input
                placeholder="Company Name"
                value={newClient.client_company}
                onChange={(e) => setNewClient({...newClient, client_company: e.target.value})}
                required
              />
              <Input
                type="email"
                placeholder="Email Address"
                value={newClient.client_email}
                onChange={(e) => setNewClient({...newClient, client_email: e.target.value})}
                required
              />
              <Input
                placeholder="Phone Number"
                value={newClient.client_phone}
                onChange={(e) => setNewClient({...newClient, client_phone: e.target.value})}
              />
              <div className="md:col-span-2 flex gap-4">
                <Button type="submit" disabled={loading}>
                  Add Client
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowAddForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        )}

        {/* Clients List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold">Your Clients</h2>
          </div>
          
          {clients.length === 0 ? (
            <div className="p-12 text-center">
              <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No clients yet</h3>
              <p className="text-gray-500 mb-4">
                Start by adding your first IT support client
              </p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Client
              </Button>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {clients.map((client) => (
                <div key={client.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div 
                        className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold"
                        style={{ backgroundColor: client.brand_primary_color }}
                      >
                        {client.client_name.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {client.client_name}
                        </h3>
                        <p className="text-sm text-gray-500">{client.client_company}</p>
                        <p className="text-sm text-gray-500">{client.client_email}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {client.chatbot_count || 0} Chatbots
                        </p>
                        <p className={`text-sm ${client.is_active ? 'text-green-600' : 'text-red-600'}`}>
                          {client.is_active ? 'Active' : 'Inactive'}
                        </p>
                      </div>
                      
                      <div className="flex space-x-2">
                        <Link href={`/clients/${client.id}/analytics`}>
                          <Button variant="outline" size="sm" title="View Analytics">
                            <BarChart3 className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/clients/${client.id}/branding`}>
                          <Button variant="outline" size="sm" title="Customize Branding">
                            <Palette className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button variant="outline" size="sm" title="Edit Client">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          title="Delete Client"
                          onClick={() => handleDeleteClient(client.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
