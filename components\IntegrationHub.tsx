// "use client";

// import { useState, useEffect } from 'react';
// import { FaWhatsapp, FaEnvelope, FaSlack, FaFacebook, FaTelegram, FaInstagram } from 'react-icons/fa';
// import { supabase } from '../supabaseClient';

// interface Integration {
//   id: string;
//   type: string;
//   status: 'connected' | 'disconnected';
//   config: any;
// }

// const INTEGRATION_TYPES = {
//   whatsapp: {
//     name: 'WhatsApp',
//     icon: FaWhatsapp,
//     color: '#25D366',
//     description: 'Connect with customers via WhatsApp Business API'
//   },
//   email: {
//     name: 'Email',
//     icon: FaEnvelope,
//     color: '#EA4335',
//     description: 'Send automated email responses'
//   },
//   slack: {
//     name: 'Slack',
//     icon: FaSlack,
//     color: '#4A154B',
//     description: 'Integrate with your Slack workspace'
//   },
//   messenger: {
//     name: 'Messenger',
//     icon: FaFacebook,
//     color: '#4267B2',
//     description: 'Connect with customers via Facebook Messenger'
//   },
//   telegram: {
//     name: 'Telegram',
//     icon: FaTelegram,
//     color: '#0088CC',
//     description: 'Connect with customers via Telegram'
//   },
//   instagram: {
//     name: 'Instagram',
//     icon: FaInstagram,
//     color: '#E1306C',
//     description: 'Connect with customers via Instagram'
//   }
// };

// // Add this type definition
// type IntegrationType = keyof typeof INTEGRATION_TYPES;

// export default function IntegrationHub() {
//   const [integrations, setIntegrations] = useState<Integration[]>([]);

//   useEffect(() => {
//     const fetchIntegrations = async () => {
//       const { data, error } = await supabase
//         .from('integrations')
//         .select('*');

//       if (error) {
//         console.error('Error fetching integrations:', error);
//         return;
//       }

//       setIntegrations(data);
//     };

//     fetchIntegrations();
//   }, []);

//   return (
//     <div className="p-4">
//       <h1 className="text-2xl font-bold mb-4">Integration Hub</h1>
//       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
//         {Object.keys(INTEGRATION_TYPES).map(type => {
//           const integrationType = type as IntegrationType;
//           return (
//             <div key={type} className="bg-white rounded-lg p-4 shadow-md">
//               <h2 className="text-lg font-bold mb-2">{INTEGRATION_TYPES[integrationType].name}</h2>
//               <p className="text-gray-600">{INTEGRATION_TYPES[integrationType].description}</p>
//               <div className="mt-4">
//                 <button className="bg-blue-500 text-white px-4 py-2 rounded-md">
//                   Connect
//                 </button>
//               </div>
//             </div>
//           );
//         })}
//       </div>
//     </div>
//   );
// } 