"use client";

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { supabase } from '../supabaseClient';
import { But<PERSON> } from './ui/button';
import { Check, Crown, Zap, Building, Star } from 'lucide-react';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  interval: 'month' | 'year';
  features: string[];
  maxChatbots: number;
  maxMessages: number;
  priority: 'basic' | 'pro' | 'enterprise';
  popular?: boolean;
}

const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: 'starter',
    name: 'Starter',
    price: 0,
    interval: 'month',
    maxChatbots: 2,
    maxMessages: 1000,
    priority: 'basic',
    features: [
      'Up to 2 chatbots',
      '1,000 messages/month',
      'Basic analytics',
      'Email support',
      'Standard response time'
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 29,
    interval: 'month',
    maxChatbots: 10,
    maxMessages: 10000,
    priority: 'pro',
    popular: true,
    features: [
      'Up to 10 chatbots',
      '10,000 messages/month',
      'Advanced analytics',
      'Priority support',
      'Custom branding',
      'API access',
      'Faster response time'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 99,
    interval: 'month',
    maxChatbots: -1, // Unlimited
    maxMessages: -1, // Unlimited
    priority: 'enterprise',
    features: [
      'Unlimited chatbots',
      'Unlimited messages',
      'Real-time analytics',
      'Dedicated support',
      'White-label solution',
      'Advanced integrations',
      'Custom features',
      'SLA guarantee'
    ]
  }
];

export default function SubscriptionManager() {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<string>('starter');
  const [usage, setUsage] = useState({
    chatbots: 0,
    messages: 0
  });

  useEffect(() => {
    if (user) {
      fetchCurrentSubscription();
      fetchUsage();
    }
  }, [user]);

  const fetchCurrentSubscription = async () => {
    // In a real app, you'd fetch this from your subscription service
    // For now, we'll assume starter plan
    setCurrentPlan('starter');
  };

  const fetchUsage = async () => {
    if (!user) return;

    try {
      // Fetch user's chatbots count
      const { data: chatbots } = await supabase
        .from('chatbots')
        .select('id')
        .eq('clerk_user_id', user.id);

      // Fetch messages count for current month
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const { data: messages } = await supabase
        .from('messages')
        .select('id')
        .in('chatbot_id', chatbots?.map(bot => bot.id) || [])
        .gte('created_at', startOfMonth.toISOString());

      setUsage({
        chatbots: chatbots?.length || 0,
        messages: messages?.length || 0
      });
    } catch (error) {
      console.error('Error fetching usage:', error);
    }
  };

  const handleSubscribe = async (planId: string) => {
    if (!user) return;

    setLoading(true);
    try {
      // In a real app, you'd integrate with Stripe or another payment processor
      console.log('Subscribing to plan:', planId);

      // For demo purposes, we'll just show an alert
      alert(`Subscription to ${planId} plan initiated! This would redirect to payment processor in a real app.`);

    } catch (error) {
      console.error('Subscription error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPlanIcon = (priority: string) => {
    switch (priority) {
      case 'basic': return <Star className="h-6 w-6" />;
      case 'pro': return <Zap className="h-6 w-6" />;
      case 'enterprise': return <Building className="h-6 w-6" />;
      default: return <Star className="h-6 w-6" />;
    }
  };

  const formatLimit = (limit: number) => {
    return limit === -1 ? 'Unlimited' : limit.toLocaleString();
  };

  return (
    <div className="space-y-8">
      {/* Current Plan & Usage */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-bold mb-6">Current Plan & Usage</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-blue-100 rounded-full p-3 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
              <Crown className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="font-semibold text-lg">Current Plan</h3>
            <p className="text-2xl font-bold text-blue-600 capitalize">{currentPlan}</p>
          </div>

          <div className="text-center">
            <div className="bg-green-100 rounded-full p-3 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
              <Building className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="font-semibold text-lg">Chatbots</h3>
            <p className="text-2xl font-bold text-green-600">
              {usage.chatbots} / {formatLimit(subscriptionPlans.find(p => p.id === currentPlan)?.maxChatbots || 0)}
            </p>
          </div>

          <div className="text-center">
            <div className="bg-purple-100 rounded-full p-3 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
              <Zap className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="font-semibold text-lg">Messages This Month</h3>
            <p className="text-2xl font-bold text-purple-600">
              {usage.messages} / {formatLimit(subscriptionPlans.find(p => p.id === currentPlan)?.maxMessages || 0)}
            </p>
          </div>
        </div>
      </div>

      {/* Subscription Plans */}
      <div>
        <h2 className="text-2xl font-bold mb-6">Choose Your Plan</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {subscriptionPlans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-white rounded-lg shadow-lg p-6 ${
                plan.popular ? 'ring-2 ring-blue-500' : ''
              } ${currentPlan === plan.id ? 'ring-2 ring-green-500' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}

              {currentPlan === plan.id && (
                <div className="absolute -top-3 right-4">
                  <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Current Plan
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <div className={`inline-flex p-3 rounded-full mb-4 ${
                  plan.priority === 'basic' ? 'bg-gray-100 text-gray-600' :
                  plan.priority === 'pro' ? 'bg-blue-100 text-blue-600' :
                  'bg-purple-100 text-purple-600'
                }`}>
                  {getPlanIcon(plan.priority)}
                </div>
                <h3 className="text-xl font-bold">{plan.name}</h3>
                <div className="mt-2">
                  <span className="text-3xl font-bold">${plan.price}</span>
                  <span className="text-gray-500">/{plan.interval}</span>
                </div>
              </div>

              <ul className="space-y-3 mb-6">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                onClick={() => handleSubscribe(plan.id)}
                disabled={loading || currentPlan === plan.id}
                className={`w-full ${
                  currentPlan === plan.id
                    ? 'bg-green-500 hover:bg-green-600'
                    : plan.priority === 'pro'
                    ? 'bg-blue-500 hover:bg-blue-600'
                    : 'bg-gray-500 hover:bg-gray-600'
                } text-white`}
              >
                {loading ? 'Processing...' :
                 currentPlan === plan.id ? 'Current Plan' :
                 plan.price === 0 ? 'Get Started' : 'Upgrade'}
              </Button>
            </div>
          ))}
        </div>
      </div>

      {/* Billing Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Billing Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-2">Next Billing Date</h4>
            <p className="text-gray-600">
              {currentPlan === 'starter' ? 'No billing required' : 'January 15, 2024'}
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">Payment Method</h4>
            <p className="text-gray-600">
              {currentPlan === 'starter' ? 'No payment method required' : '**** **** **** 1234'}
            </p>
          </div>
        </div>

        {currentPlan !== 'starter' && (
          <div className="mt-4 pt-4 border-t">
            <Button variant="outline" className="mr-3">
              Update Payment Method
            </Button>
            <Button variant="outline" className="text-red-600 border-red-300 hover:bg-red-50">
              Cancel Subscription
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}