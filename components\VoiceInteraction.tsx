// "use client";

// import { useState, useEffect, useRef } from 'react';
// import { FaMicrophone, FaStop, FaVolumeUp, FaVolumeOff } from 'react-icons/fa';

// interface VoiceConfig {
//   enabled: boolean;
//   voice: string;
//   language: string;
//   speed: number;
//   pitch: number;
// }

// export default function VoiceInteraction({ botId }: { botId: string }) {
//   const [isListening, setIsListening] = useState(false);
//   const [transcript, setTranscript] = useState('');
//   const [voiceConfig, setVoiceConfig] = useState<VoiceConfig>({
//     enabled: false,
//     voice: 'en-US-Standard-A',
//     language: 'en-US',
//     speed: 1,
//     pitch: 1
//   });

//   const recognition = useRef<any>(null);
//   const synthesis = useRef<any>(null);

//   useEffect(() => {
//     // Initialize Web Speech API
//     if (typeof window !== 'undefined') {
//       const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
//       recognition.current = new SpeechRecognition();
//       recognition.current.continuous = true;
//       recognition.current.interimResults = true;

//       recognition.current.onresult = (event: any) => {
//         const transcript = Array.from(event.results)
//           .map((result: any) => result[0])
//           .map(result => result.transcript)
//           .join('');

//         setTranscript(transcript);
//       };

//       synthesis.current = window.speechSynthesis;
//     }
//   }, []);

//   const toggleListening = () => {
//     if (isListening) {
//       recognition.current.stop();
//     } else {
//       recognition.current.start();
//     }
//     setIsListening(!isListening);
//   };

//   const speak = (text: string) => {
//     if (synthesis.current) {
//       const utterance = new SpeechSynthesisUtterance(text);
//       utterance.voice = synthesis.current.getVoices().find(
//         (voice: SpeechSynthesisVoice) => voice.name === voiceConfig.voice
//       );
//       utterance.rate = voiceConfig.speed;
//       utterance.pitch = voiceConfig.pitch;
//       synthesis.current.speak(utterance);
//     }
//   };

//   return (
//     <div className="space-y-6">
//       {/* Voice Configuration */}
//       <div className="bg-white rounded-lg shadow p-6">
//         <h2 className="text-xl font-bold mb-4">Voice Settings</h2>
        
//         <div className="space-y-4">
//           {/* Enable/Disable Voice */}
//           <div className="flex items-center justify-between">
//             <div>
//               <h3 className="font-medium">Enable Voice Interaction</h3>
//               <p className="text-sm text-gray-500">
//                 Allow users to interact with your bot using voice
//               </p>
//             </div>
//             <label className="relative inline-flex items-center cursor-pointer">
//               <input
//                 type="checkbox"
//                 checked={voiceConfig.enabled}
//                 onChange={(e) => setVoiceConfig({
//                   ...voiceConfig,
//                   enabled: e.target.checked
//                 })}
//                 className="sr-only peer"
//               />
//               <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:bg-blue-600 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
//             </label>
//           </div>

//           {/* Voice Selection */}
//           <div>
//             <label className="block text-sm font-medium mb-2">Voice</label>
//             <select
//               value={voiceConfig.voice}
//               onChange={(e) => setVoiceConfig({
//                 ...voiceConfig,
//                 voice: e.target.value
//               })}
//               className="w-full p-2 border rounded"
//             >
//               {synthesis.current?.getVoices().map((voice: SpeechSynthesisVoice) => (
//                 <option key={voice.name} value={voice.name}>
//                   {voice.name} ({voice.lang})
//                 </option>
//               ))}
//             </select>
//           </div>

//           {/* Speed & Pitch Controls */}
//           <div className="grid grid-cols-2 gap-4">
//             <div>
//               <label className="block text-sm font-medium mb-2">Speed</label>
//               <input
//                 type="range"
//                 min="0.5"
//                 max="2"
//                 step="0.1"
//                 value={voiceConfig.speed}
//                 onChange={(e) => setVoiceConfig({
//                   ...voiceConfig,
//                   speed: parseFloat(e.target.value)
//                 })}
//                 className="w-full"
//               />
//             </div>
//             <div>
//               <label className="block text-sm font-medium mb-2">Pitch</label>
//               <input
//                 type="range"
//                 min="0.5"
//                 max="2"
//                 step="0.1"
//                 value={voiceConfig.pitch}
//                 onChange={(e) => setVoiceConfig({
//                   ...voiceConfig,
//                   pitch: parseFloat(e.target.value)
//                 })}
//                 className="w-full"
//               />
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Voice Interaction */}
//       <div className="flex items-center justify-center">
//         <button
//           onClick={toggleListening}
//           className="p-4 bg-blue-500 text-white rounded-full hover:bg-blue-600"
//         >
//           {isListening ? (
//             <FaStop className="text-2xl" />
//           ) : (
//             <FaMicrophone className="text-2xl" />
//           )}
//         </button>
//       </div>

//       {/* Transcript */}
//       <div className="bg-white rounded-lg shadow p-6">
//         <h2 className="text-xl font-bold mb-4">Transcript</h2>
        
//         <div className="space-y-4">
//           <textarea
//             value={transcript}
//             onChange={(e) => setTranscript(e.target.value)}
//             className="w-full p-2 border rounded"
//             rows={4}
//             placeholder="Transcript"
//           />
//         </div>
//       </div>
//     </div>
//   );
// } 