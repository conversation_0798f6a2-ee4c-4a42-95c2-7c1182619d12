import { supabase } from "../supabaseClient";

export async function trackBotUsage(botId: string, sessionId: string) {
  const today = new Date().toISOString().split('T')[0];

  try {
    // Update daily analytics
    const { data: existingAnalytics } = await supabase
      .from('analytics')
      .select('*')
      .eq('chatbot_id', botId)
      .eq('date', today)
      .single();

    if (existingAnalytics) {
      await supabase
        .from('analytics')
        .update({
          total_messages: existingAnalytics.total_messages + 1,
          unique_users: existingAnalytics.unique_users + (existingAnalytics.sessions?.includes(sessionId) ? 0 : 1),
          sessions: [...(existingAnalytics.sessions || []), sessionId]
        })
        .eq('id', existingAnalytics.id);
    } else {
      await supabase
        .from('analytics')
        .insert({
          chatbot_id: botId,
          date: today,
          total_messages: 1,
          unique_users: 1,
          sessions: [sessionId]
        });
    }

    // Check usage limits
    const { data: botData } = await supabase
      .from('chatbots')
      .select('users!inner(plan_id)')
      .eq('id', botId)
      .single();

    if (botData) {
      const { data: planData } = await supabase
        .from('plans')
        .select('message_limit')
        .eq('id', botData.users[0].plan_id)
        .single();

      if (planData && existingAnalytics?.total_messages >= planData.message_limit) {
        throw new Error('Message limit reached for current plan');
      }
    }
  } catch (error) {
    console.error('Error tracking usage:', error);
    throw error;
  }
} 