import { NextRequest, NextResponse } from 'next/server';
import { Groq } from 'groq-sdk';

// Initialize Groq client on the server side
const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { messages, systemPrompt, name } = await request.json();

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      );
    }

    // Create the completion with Groq
    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "assistant",
          content: `You are a helpful assistant talking to ${name}. ${systemPrompt}`,
        },
        ...messages,
      ] as const,
      model: "llama3-8b-8192",
      temperature: 1,
      max_tokens: 1024,
    });

    const aiResponse = completion.choices[0]?.message?.content?.trim();

    if (!aiResponse) {
      return NextResponse.json(
        { error: 'No response generated' },
        { status: 500 }
      );
    }

    return NextResponse.json({ response: aiResponse });
  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { error: 'Failed to generate response' },
      { status: 500 }
    );
  }
}
