{"name": "saffabot-ai-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "{": "gql-gen --config codegen.yml"}, "dependencies": {"@clerk/nextjs": "^6.2.1", "@dicebear/collection": "^9.2.2", "@dicebear/core": "^9.2.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.46.1", "body-parser": "^1.20.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "daisyui": "^4.12.13", "express": "^4.21.1", "groq": "^3.62.3", "groq-sdk": "^0.7.0", "lucide-react": "^0.453.0", "next": "^14.2.17", "next-auth": "^4.24.10", "next-themes": "^0.3.0", "or": "^0.2.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.53.1", "react-markdown": "^9.0.1", "react-timeago": "^7.2.0", "remark-gfm": "^4.0.0", "sonner": "^1.5.0", "stripe": "^18.3.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-timeago": "^4.1.7", "eslint": "^8", "eslint-config-next": "^15.0.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.6.3"}}