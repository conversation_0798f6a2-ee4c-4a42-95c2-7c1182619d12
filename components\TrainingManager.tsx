// "use client";

// import { useState, useEffect } from 'react';
// import { supabase } from '../supabaseClient';
// // import { FaUpload, FaDownload, FaTrash, FaEdit } from 'react-icons/fa';

// interface TrainingData {
//   id: string;
//   category: string;
//   question: string;
//   answer: string;
//   confidence: number;
//   usage_count: number;
//   last_used: string;
// }

// export default function TrainingManager({ botId }: { botId: string }) {
//   const [trainingData, setTrainingData] = useState<TrainingData[]>([]);
//   const [categories, setCategories] = useState<string[]>([]);
//   const [selectedCategory, setSelectedCategory] = useState<string>('all');
//   const [isImporting, setIsImporting] = useState(false);
//   const [editingItem, setEditingItem] = useState<TrainingData | null>(null);

//   useEffect(() => {
//     loadTrainingData();
//   }, [botId]);

//   const loadTrainingData = async () => {
//     const { data, error } = await supabase
//       .from('training_data')
//       .select('*')
//       .eq('chatbot_id', botId);

//     if (data) {
//       setTrainingData(data);
//       const uniqueCategories = [...new Set(data.map(item => item.category))];
//       setCategories(uniqueCategories);
//     }
//   };

//   const handleImport = async (file: File) => {
//     setIsImporting(true);
//     try {
//       const content = await file.text();
//       const data = JSON.parse(content);
      
//       // Validate and process training data
//       const processedData = data.map((item: any) => ({
//         chatbot_id: botId,
//         category: item.category || 'General',
//         question: item.question,
//         answer: item.answer,
//         confidence: 1.0,
//         usage_count: 0
//       }));

//       const { error } = await supabase
//         .from('training_data')
//         .insert(processedData);

//       if (!error) {
//         loadTrainingData();
//       }
//     } catch (error) {
//       console.error('Import error:', error);
//     } finally {
//       setIsImporting(false);
//     }
//   };

//   return (
//     <div className="space-y-6">
//       {/* Training Data Controls */}
//       <div className="bg-white rounded-lg shadow p-6">
//         <div className="flex justify-between items-center mb-6">
//           <h2 className="text-xl font-bold">Training Data Management</h2>
          
//           <div className="flex gap-4">
//             <button
//               onClick={() => document.getElementById('file-upload')?.click()}
//               className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
//             >
//               <FaUpload />
//               Import Data
//             </button>
//             <input
//               id="file-upload"
//               type="file"
//               accept=".json"
//               className="hidden"
//               onChange={(e) => e.target.files?.[0] && handleImport(e.target.files[0])}
//             />
            
//             <button
//               onClick={() => {
//                 const dataStr = JSON.stringify(trainingData, null, 2);
//                 const dataBlob = new Blob([dataStr], { type: 'application/json' });
//                 const url = URL.createObjectURL(dataBlob);
//                 const a = document.createElement('a');
//                 a.href = url;
//                 a.download = 'training-data.json';
//                 a.click();
//               }}
//               className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
//             >
//               <FaDownload />
//               Export Data
//             </button>
//           </div>
//         </div>

//         {/* Category Filter */}
//         <div className="flex gap-4 mb-6">
//           <button
//             onClick={() => setSelectedCategory('all')}
//             className={`px-4 py-2 rounded-lg ${
//               selectedCategory === 'all'
//                 ? 'bg-blue-500 text-white'
//                 : 'bg-gray-100'
//             }`}
//           >
//             All
//           </button>
//           {categories.map(category => (
//             <button
//               key={category}
//               onClick={() => setSelectedCategory(category)}
//               className={`px-4 py-2 rounded-lg ${
//                 selectedCategory === category
//                   ? 'bg-blue-500 text-white'
//                   : 'bg-gray-100'
//               }`}
//             >
//               {category}
//             </button>
//           ))}
//         </div>

//         {/* Training Data Table */}
//         <div className="overflow-x-auto">
//           <table className="w-full">
//             <thead>
//               <tr className="bg-gray-50">
//                 <th className="px-4 py-2 text-left">Question</th>
//                 <th className="px-4 py-2 text-left">Answer</th>
//                 <th className="px-4 py-2 text-left">Category</th>
//                 <th className="px-4 py-2 text-center">Confidence</th>
//                 <th className="px-4 py-2 text-center">Usage</th>
//                 <th className="px-4 py-2 text-center">Actions</th>
//               </tr>
//             </thead>
//             <tbody>
//               {trainingData
//                 .filter(item => selectedCategory === 'all' || item.category === selectedCategory)
//                 .map(item => (
//                   <tr key={item.id} className="border-b border-gray-200">
//                     <td className="px-4 py-2">{item.question}</td>
//                     <td className="px-4 py-2">{item.answer}</td>
//                     <td className="px-4 py-2">{item.category}</td>
//                     <td className="px-4 py-2 text-center">{item.confidence}</td>
//                     <td className="px-4 py-2 text-center">{item.usage_count}</td>
//                     <td className="px-4 py-2 text-center">
//                       <div className="flex gap-2">
//                         <button
//                           onClick={() => setEditingItem(item)}
//                           className="text-blue-500 hover:text-blue-700"
//                         >
//                           <FaEdit />
//                         </button>
//                         <button
//                           onClick={() => {
//                             // Implement delete functionality
//                           }}
//                           className="text-red-500 hover:text-red-700"
//                         >
//                           <FaTrash />
//                         </button>
//                       </div>
//                     </td>
//                   </tr>
//                 ))}
//             </tbody>
//           </table>
//         </div>
//       </div>
//     </div>
//   );
// } 