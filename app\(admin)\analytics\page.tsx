import AdminMetricsCard from '../../../components/AdminMetricsCard';
import { BarChart3, MessageSquare, Bot, Users, TrendingUp, Target, Activity } from 'lucide-react';

// Simple analytics component with static data for now
function AnalyticsMetrics() {
  const metrics = {
    totalChatbots: 3,
    totalMessages: 1250,
    totalSessions: 89,
    uniqueUsers: 45,
    weeklyMessages: 245,
    weeklySessions: 18,
    avgMessagesPerSession: '14.0'
  };



  return (
      <div className="space-y-8">
        {/* Overview Metrics */}
        <div>
          <h2 className="text-2xl font-bold mb-6">Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <AdminMetricsCard
              title="Total Chatbots"
              value={metrics.totalChatbots}
              icon={Bot}
            />
            <AdminMetricsCard
              title="Total Messages"
              value={metrics.totalMessages}
              icon={MessageSquare}
            />
            <AdminMetricsCard
              title="Total Sessions"
              value={metrics.totalSessions}
              icon={Users}
            />
            <AdminMetricsCard
              title="Unique Users"
              value={metrics.uniqueUsers}
              icon={Target}
            />
          </div>
        </div>

        {/* Performance Metrics */}
        <div>
          <h2 className="text-2xl font-bold mb-6">Performance</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <AdminMetricsCard
              title="Weekly Messages"
              value={metrics.weeklyMessages}
              icon={BarChart3}
            />
            <AdminMetricsCard
              title="Weekly Sessions"
              value={metrics.weeklySessions}
              icon={TrendingUp}
            />
            <AdminMetricsCard
              title="Avg Messages/Session"
              value={metrics.avgMessagesPerSession}
              icon={Activity}
            />
            <AdminMetricsCard
              title="Active Chatbots"
              value={metrics.totalChatbots}
              icon={Bot}
            />
          </div>
        </div>

        {/* Time-based Analytics */}
        <div>
          <h2 className="text-2xl font-bold mb-6">Recent Activity</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Weekly Summary</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Messages (7d)</span>
                  <span className="font-bold">{metrics.weeklyMessages}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Sessions (7d)</span>
                  <span className="font-bold">{metrics.weeklySessions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Daily Average</span>
                  <span className="font-bold">{metrics.weeklyMessages > 0 ? (metrics.weeklyMessages / 7).toFixed(1) : '0'}</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Overall Stats</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Messages</span>
                  <span className="font-bold">{metrics.totalMessages}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Active Bots</span>
                  <span className="font-bold">{metrics.totalChatbots}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Engagement</span>
                  <span className="font-bold text-green-600">
                    {metrics.totalSessions > 0 ? 'Active' : 'Getting Started'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
}

export default function AnalyticsPage() {
  return (
    <div className="flex-1 p-10">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Analytics Dashboard</h1>

        <AnalyticsMetrics />

        {/* Advanced Analytics Component - Temporarily Disabled */}
        <div className="mt-12">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-blue-800 mb-2">Advanced Analytics</h2>
            <p className="text-blue-700">
              Advanced analytics features are being optimized and will be available soon.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export const dynamic = "force-dynamic";
