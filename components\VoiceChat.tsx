import React, { useState, useEffect } from 'react';

// Define the Message interface
interface Message {
    text?: string;
    voice?: string;
    type: 'user' | 'bot';
}

// Add this type declaration at the top of your file
type webkitSpeechRecognition = typeof window.SpeechRecognition;

declare global {
    interface Window {
        webkitSpeechRecognition: any; // Declare webkitSpeechRecognition on the Window interface
        SpeechRecognition: any; // Change this line to avoid circular reference
    }
}

// Add this type declaration at the top of your file
interface SpeechRecognitionEvent extends Event {
    results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent extends Event {
    error: string; // Define the error property
}

const VoiceChat = () => {
    const [isListening, setIsListening] = useState(false);
    const [recognition, setRecognition] = useState<webkitSpeechRecognition | null>(null);
    const [chatHistory, setChatHistory] = useState<Message[]>([]); // To store chat messages

    // Initialize Speech Recognition
    useEffect(() => {
        if ('webkitSpeechRecognition' in window) {
            const SpeechRecognition = window.webkitSpeechRecognition as typeof window.SpeechRecognition;
            const recognitionInstance = new SpeechRecognition();
            recognitionInstance.continuous = false;
            recognitionInstance.interimResults = false;

            recognitionInstance.onstart = () => {
            
            };

            recognitionInstance.onresult = async (event: SpeechRecognitionEvent) => {
                const transcript = event.results[0][0].transcript;
             
                await sendToGroq(transcript); // Send the recognized text to Groq

                // Optionally, you can also send the voice message URL if needed
                const voiceMessageUrl = await getVoiceMessageUrl(transcript); // Implement this function to get the voice URL
                setChatHistory((prev) => [...prev, { voice: voiceMessageUrl, type: 'user' }]); // Update chat history with voice message
            };

            recognitionInstance.onerror = (event: SpeechRecognitionErrorEvent) => {
                const error = event.error;
               
            };

            recognitionInstance.onend = () => {
      
                setIsListening(false);
            };

            setRecognition(recognitionInstance);
        } else {
           
        }
    }, []);

    const startListening = () => {
        if (recognition) {
            setIsListening(true);
            recognition.start();
        }
    };

    const stopListening = () => {
        if (recognition) {
            recognition.stop();
        }
    };

    // Step 2: Send the recognized text to Groq
    const sendToGroq = async (message: any) => {
        try {
            const response = await fetch('YOUR_GROQ_API_ENDPOINT', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message }), // Adjust based on your API structure
            });

            const data = await response.json();
         
            setChatHistory((prev) => [...prev, { text: message, type: 'user' }, { text: data.response, type: 'bot' }]); // Update chat history
            speak(data.response); // Step 5: Speak the response
        } catch (error) {
          
        }
    };

    // Step 5: Implement Text-to-Speech
    const speak = (text: string | undefined) => {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'en-US'; // Set the language
        speechSynthesis.speak(utterance);
    };

    // Function to get the voice message URL (implement as needed)
    const getVoiceMessageUrl = async (transcript: string): Promise<string> => {
        // Logic to convert transcript to voice message URL
        return 'URL_TO_VOICE_MESSAGE'; // Replace with actual implementation
    };

    return (
        <div>
            <button onClick={isListening ? stopListening : startListening}>
                {isListening ? 'Stop Listening' : 'Start Listening'}
            </button>
            <div>
                <h2>Chat History</h2>
                <ul>
                    {chatHistory.map((msg: Message, index) => (
                        <li key={index} style={{ color: msg.type === 'user' ? 'blue' : 'green' }}>
                            {msg.text}
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};

export default VoiceChat;
