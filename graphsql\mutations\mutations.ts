import { supabase } from '../../supabaseClient'; // Ensure this is set up correctly

// Types for all mutations
interface ChatbotInput {
  clerk_user_id: string;
  name: string;
}

interface CharacteristicInput {
  chatbot_id: number;
  content: string;
}

interface GuestInput {
  name: string;
  email: string;
}

interface ChatSessionInput {
  chatbot_id: number;
  guest_id: number;
}

interface MessageInput {
  chat_session_id: number;
  content: string;
  sender: 'user' | 'ai';
}

// Utility function to handle error logging and throwing
const handleError = (error: any) => {
  console.error('Mutation error:', error);
  throw new Error(error?.message || 'An error occurred during the mutation.');
};

// Helper to generate timestamp for created_at fields
const generateTimestamp = () => new Date().toISOString();

// Chatbot Mutations
export const createChatbot = async (input: ChatbotInput) => {
  try {
    const { data, error } = await supabase
      .from('chatbots')
      .insert([
        {
          clerk_user_id: input.clerk_user_id,
          name: input.name,
          created_at: generateTimestamp(),
        },
      ])
      .single();

    if (error) handleError(error);
    return data;
  } catch (error) {
    handleError(error);
  }
};

// Characteristic Mutation
export const addCharacteristic = async (input: CharacteristicInput) => {
  try {
    const { data, error } = await supabase
      .from('chatbot_characteristics')
      .insert([
        {
          chatbot_id: input.chatbot_id,
          content: input.content,
          created_at: generateTimestamp(),
        },
      ])
      .single();

    if (error) handleError(error);
    return data;
  } catch (error) {
    handleError(error);
  }
};

// Create Chat Session Mutation
export const createChatSession = async (input: ChatSessionInput) => {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .insert([
        {
          chatbot_id: input.chatbot_id,
          guest_id: input.guest_id,
          created_at: generateTimestamp(),
        },
      ])
      .single();

    if (error) handleError(error);
    return data;
  } catch (error) {
    handleError(error);
  }
};

// Create Message Mutation
export const createMessage = async (input: MessageInput) => {
  try {
    const { data, error } = await supabase
      .from('messages')
      .insert([
        {
          chat_session_id: input.chat_session_id,
          content: input.content,
          sender: input.sender,
          created_at: generateTimestamp(),
        },
      ])
      .single();

    if (error) handleError(error);
    return data;
  } catch (error) {
    handleError(error);
  }
};
// Function to update the chat session status
export const updateChatSessionStatus = async (chatSessionId: string, status: 'active' | 'closed' | 'archived') => {
    const { data, error } = await supabase
      .from('chat_sessions')
      .update({ status })
      .eq('id', chatSessionId)
      .single();
  
    if (error) {
      throw new Error(`Error updating chat session status: ${error.message}`);
    }
    return data;
  };
  