import { But<PERSON> } from "../../components/ui/button";  // Import custom Button component
import Link from "next/link";  // Import Link for navigation
import { Suspense } from "react";
import { auth } from "@clerk/nextjs/server";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import AdminMetricsCard from "../../components/AdminMetricsCard";
import { MessageSquare, Bot, Users, TrendingUp } from "lucide-react";

// Server component to fetch metrics
async function DashboardMetrics() {
  const { userId } = await auth();

  if (!userId) {
    return null;
  }

  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  try {
    // Fetch user's chatbots
    const { data: chatbots } = await supabase
      .from('chatbots')
      .select('id')
      .eq('clerk_user_id', userId);

    // Fetch total messages across all user's chatbots
    const { data: messages } = await supabase
      .from('messages')
      .select('id, created_at')
      .in('chatbot_id', chatbots?.map(bot => bot.id) || []);

    // Fetch total chat sessions
    const { data: sessions } = await supabase
      .from('chat_sessions')
      .select('id, created_at')
      .in('chatbot_id', chatbots?.map(bot => bot.id) || []);

    // Calculate metrics
    const totalChatbots = chatbots?.length || 0;
    const totalMessages = messages?.length || 0;
    const totalSessions = sessions?.length || 0;

    // Calculate messages from last 7 days for growth
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentMessages = messages?.filter(msg =>
      new Date(msg.created_at) > sevenDaysAgo
    ).length || 0;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <AdminMetricsCard
          title="Total Chatbots"
          value={totalChatbots}
          icon={Bot}
        />
        <AdminMetricsCard
          title="Total Messages"
          value={totalMessages}
          icon={MessageSquare}
        />
        <AdminMetricsCard
          title="Chat Sessions"
          value={totalSessions}
          icon={Users}
        />
        <AdminMetricsCard
          title="Messages (7 days)"
          value={recentMessages}
          icon={TrendingUp}
        />
      </div>
    );
  } catch (error) {
    console.error('Error fetching metrics:', error);
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
        <p className="text-red-600">Unable to load dashboard metrics</p>
      </div>
    );
  }
}

export default function Home() {
  return (
    <main className="p-10 bg-white m-10 rounded-md w-full max-w-6xl mx-auto">
      {/* Main heading */}
      <h1 className="text-4xl font-light text-gray-900 mb-2">
        Welcome to{" "}
        <span className="text-[#64B5F5] font-semibold">SaffaBot-AI Agent App</span>
      </h1>

      {/* Subheading */}
      <h2 className="mb-8 text-xl text-gray-600">
        Your customizable AI chat agent that helps you manage your customer conversations.
      </h2>

      {/* Dashboard Metrics */}
      <Suspense fallback={
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-gray-100 rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-300 rounded mb-2"></div>
              <div className="h-8 bg-gray-300 rounded"></div>
            </div>
          ))}
        </div>
      }>
        <DashboardMetrics />
      </Suspense>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Link href="/create-chatbot" className="block">
          <div className="bg-[#64B5F5] hover:bg-[#42A5F5] text-white p-6 rounded-lg transition-colors">
            <h3 className="text-lg font-semibold mb-2">Create New Chatbot</h3>
            <p className="text-blue-100">Set up a new AI assistant for your business</p>
          </div>
        </Link>

        <Link href="/view-chatbots" className="block">
          <div className="bg-green-500 hover:bg-green-600 text-white p-6 rounded-lg transition-colors">
            <h3 className="text-lg font-semibold mb-2">Manage Chatbots</h3>
            <p className="text-green-100">View and edit your existing chatbots</p>
          </div>
        </Link>

        <Link href="/review-sessions" className="block">
          <div className="bg-purple-500 hover:bg-purple-600 text-white p-6 rounded-lg transition-colors">
            <h3 className="text-lg font-semibold mb-2">Review Sessions</h3>
            <p className="text-purple-100">Analyze customer conversations</p>
          </div>
        </Link>
      </div>

      {/* Getting Started Section */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Getting Started</h3>
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 bg-[#64B5F5] text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
            <span>Create your first chatbot with custom characteristics</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 bg-[#64B5F5] text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
            <span>Embed the chatbot on your website or share the link</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 bg-[#64B5F5] text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
            <span>Monitor conversations and improve your bot&apos;s performance</span>
          </div>
        </div>
      </div>
    </main>
  );
}