# Stripe Setup Instructions for SaffaBot

## 🚀 Quick Setup Guide

### 1. Create Stripe Account
1. Go to [stripe.com](https://stripe.com)
2. Sign up for a Stripe account
3. Complete business verification for South Africa

### 2. Get API Keys
1. Go to Stripe Dashboard → Developers → API Keys
2. Copy your **Publishable Key** and **Secret Key**
3. Update `.env.local`:
   ```
   STRIPE_SECRET_KEY=sk_test_your_actual_secret_key
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key
   ```

### 3. Create Products & Prices in Stripe
1. Go to Stripe Dashboard → Products
2. Create two products:

#### Professional Plan
- Name: "SaffaBot Professional"
- Price: R499.00 ZAR
- Billing: Monthly recurring
- Copy the Price ID → Update `STRIPE_PROFESSIONAL_PRICE_ID`

#### Enterprise Plan  
- Name: "SaffaBot Enterprise"
- Price: R1,799.00 ZAR
- Billing: Monthly recurring
- Copy the Price ID → Update `STRIPE_ENTERPRISE_PRICE_ID`

### 4. Configure Webhooks (Optional for now)
1. Go to Stripe Dashboard → Developers → Webhooks
2. Add endpoint: `https://your-domain.com/api/stripe-webhook`
3. Select events: `checkout.session.completed`, `invoice.payment_succeeded`

### 5. Test Payment Flow
1. Start your app: `npm run dev`
2. Go to `/admin/subscription`
3. Click "Upgrade" on Professional plan
4. Use Stripe test card: `4242 4242 4242 4242`
5. Verify redirect to Stripe checkout

## 🎯 Current Status
- ✅ Stripe integration code ready
- ✅ ZAR currency support
- ✅ Checkout session API
- ⚠️ Need actual Stripe keys
- ⚠️ Need to create products in Stripe Dashboard

## 💡 Next Steps
1. Set up actual Stripe account
2. Create products with ZAR pricing
3. Update environment variables
4. Test with real Stripe checkout
