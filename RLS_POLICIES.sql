-- Row Level Security Policies for SaffaBot Multi-Client Support
-- Copy and paste this into Supabase SQL Editor

-- Enable RLS on clients table
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own clients
CREATE POLICY "Users can view their own clients" ON clients
    FOR SELECT USING (auth.uid()::text = it_business_user_id);

-- Policy: Users can insert their own clients
CREATE POLICY "Users can insert their own clients" ON clients
    FOR INSERT WITH CHECK (auth.uid()::text = it_business_user_id);

-- Policy: Users can update their own clients
CREATE POLICY "Users can update their own clients" ON clients
    FOR UPDATE USING (auth.uid()::text = it_business_user_id);

-- Policy: Users can delete their own clients
CREATE POLICY "Users can delete their own clients" ON clients
    FOR DELETE USING (auth.uid()::text = it_business_user_id);

-- Enable RLS on client_branding table
ALTER TABLE client_branding ENABLE ROW LEVEL SECURITY;

-- Policy: Users can manage branding for their own clients
CREATE POLICY "Users can manage their clients' branding" ON client_branding
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM clients 
            WHERE clients.id = client_branding.client_id 
            AND clients.it_business_user_id = auth.uid()::text
        )
    );

-- Enable RLS on client_usage_analytics table
ALTER TABLE client_usage_analytics ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view analytics for their own clients
CREATE POLICY "Users can view their clients' analytics" ON client_usage_analytics
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM clients 
            WHERE clients.id = client_usage_analytics.client_id 
            AND clients.it_business_user_id = auth.uid()::text
        )
    );

-- Update chatbots table policies to include client_id
-- First, let's check if chatbots already has RLS enabled and add client-aware policies

-- Policy: Users can view chatbots they own or that belong to their clients
DROP POLICY IF EXISTS "Users can view their own chatbots" ON chatbots;
CREATE POLICY "Users can view their own chatbots" ON chatbots
    FOR SELECT USING (
        clerk_user_id = auth.uid()::text 
        OR 
        (client_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM clients 
            WHERE clients.id = chatbots.client_id 
            AND clients.it_business_user_id = auth.uid()::text
        ))
    );

-- Policy: Users can insert chatbots for themselves or their clients
DROP POLICY IF EXISTS "Users can insert their own chatbots" ON chatbots;
CREATE POLICY "Users can insert their own chatbots" ON chatbots
    FOR INSERT WITH CHECK (
        clerk_user_id = auth.uid()::text 
        AND 
        (client_id IS NULL OR EXISTS (
            SELECT 1 FROM clients 
            WHERE clients.id = chatbots.client_id 
            AND clients.it_business_user_id = auth.uid()::text
        ))
    );

-- Policy: Users can update chatbots they own or that belong to their clients
DROP POLICY IF EXISTS "Users can update their own chatbots" ON chatbots;
CREATE POLICY "Users can update their own chatbots" ON chatbots
    FOR UPDATE USING (
        clerk_user_id = auth.uid()::text 
        OR 
        (client_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM clients 
            WHERE clients.id = chatbots.client_id 
            AND clients.it_business_user_id = auth.uid()::text
        ))
    );

-- Policy: Users can delete chatbots they own or that belong to their clients
DROP POLICY IF EXISTS "Users can delete their own chatbots" ON chatbots;
CREATE POLICY "Users can delete their own chatbots" ON chatbots
    FOR DELETE USING (
        clerk_user_id = auth.uid()::text 
        OR 
        (client_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM clients 
            WHERE clients.id = chatbots.client_id 
            AND clients.it_business_user_id = auth.uid()::text
        ))
    );
