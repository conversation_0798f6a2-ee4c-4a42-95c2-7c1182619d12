"use client";

import { useEffect, useState } from 'react';
import { supabase } from '../supabaseClient';

export default function BotPerformanceMetrics({ botId }: { botId: string }) {
  const [metrics, setMetrics] = useState({
    totalMessages: 0,
    averageResponseTime: 0,
    uniqueUsers: 0,
    popularTopics: [] as string[],
    satisfaction: 0,
    totalSessions: 0,
    recentMessages: 0
  });

  useEffect(() => {
    fetchMetrics();
  }, [botId]);

  const fetchMetrics = async () => {
    try {
      // Fetch messages and chat sessions for this bot
      const [messagesData, sessionsData] = await Promise.all([
        supabase
          .from('messages')
          .select('*')
          .eq('chatbot_id', botId),
        supabase
          .from('chat_sessions')
          .select('id, guest_id, created_at')
          .eq('chatbot_id', botId)
      ]);

      // Calculate metrics
      const totalMessages = messagesData.data?.length || 0;
      const totalSessions = sessionsData.data?.length || 0;

      // Calculate unique users (based on unique guest_ids)
      const uniqueGuestIds = new Set(
        sessionsData.data?.map(session => session.guest_id).filter(Boolean) || []
      );
      const uniqueUsers = uniqueGuestIds.size;

      // Calculate messages in last 7 days
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const recentMessages = messagesData.data?.filter(msg =>
        new Date(msg.created_at) > sevenDaysAgo
      ).length || 0;

      setMetrics({
        totalMessages,
        averageResponseTime: 0, // We don't have response time data yet
        uniqueUsers,
        popularTopics: [], // We don't have topic data yet
        satisfaction: 0, // We don't have satisfaction data yet
        totalSessions,
        recentMessages
      });
    } catch (error) {
      console.error('Error fetching metrics:', error);
    }
  };



  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center">
          <p className="text-sm text-gray-500">Total Messages</p>
          <p className="text-lg font-bold text-[#64B5F5]">{metrics.totalMessages}</p>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-500">Unique Users</p>
          <p className="text-lg font-bold text-green-600">{metrics.uniqueUsers}</p>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-500">Recent (7d)</p>
          <p className="text-lg font-bold text-purple-600">{metrics.recentMessages}</p>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-500">Sessions</p>
          <p className="text-lg font-bold text-orange-600">{metrics.totalSessions}</p>
        </div>
      </div>
    </div>
  );
} 