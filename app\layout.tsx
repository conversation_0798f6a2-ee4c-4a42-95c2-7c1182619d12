import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { Toaster } from "sonner";

export const metadata: Metadata = {
  title: "SaffaBot",
  description: "Chat the Saffa way",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="bg-gray-100">
        <ClerkProvider>
          {children}
          <Toaster position="bottom-center" />
        </ClerkProvider>
      </body>
    </html>
  );
}