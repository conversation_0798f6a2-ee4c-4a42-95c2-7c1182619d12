'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useParams } from 'next/navigation';
import { supabase } from '../../../../../supabaseClient';
import { Button } from '../../../../../components/ui/button';
import { ArrowLeft, MessageSquare, Users, Bot, Calendar, TrendingUp, Download } from 'lucide-react';
import Link from 'next/link';

interface Client {
  id: number;
  client_name: string;
  client_company: string;
}

interface ClientAnalytics {
  totalMessages: number;
  totalSessions: number;
  uniqueUsers: number;
  totalChatbots: number;
  weeklyMessages: number;
  weeklySessions: number;
  monthlyMessages: number;
  monthlySessions: number;
  avgMessagesPerSession: number;
}

interface ChatbotUsage {
  chatbot_id: number;
  chatbot_name: string;
  messages: number;
  sessions: number;
  unique_users: number;
}

interface DailyUsage {
  date: string;
  messages: number;
  sessions: number;
}

export default function ClientAnalyticsPage() {
  const { user } = useUser();
  const params = useParams();
  const clientId = Number(params.id);

  const [client, setClient] = useState<Client | null>(null);
  const [analytics, setAnalytics] = useState<ClientAnalytics>({
    totalMessages: 0,
    totalSessions: 0,
    uniqueUsers: 0,
    totalChatbots: 0,
    weeklyMessages: 0,
    weeklySessions: 0,
    monthlyMessages: 0,
    monthlySessions: 0,
    avgMessagesPerSession: 0
  });
  const [chatbotUsage, setChatbotUsage] = useState<ChatbotUsage[]>([]);
  const [dailyUsage, setDailyUsage] = useState<DailyUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState<'7d' | '30d' | '90d'>('30d');

  useEffect(() => {
    if (user && clientId) {
      fetchClientAnalytics();
    }
  }, [user, clientId, timeframe]);

  const fetchClientAnalytics = async () => {
    if (!user) return;

    try {
      // Fetch client details
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('*')
        .eq('id', clientId)
        .eq('it_business_user_id', user.id)
        .single();

      if (clientError) throw clientError;
      setClient(clientData);

      // Fetch client's chatbots
      const { data: chatbots, error: chatbotsError } = await supabase
        .from('chatbots')
        .select('id, name')
        .eq('client_id', clientId);

      if (chatbotsError) throw chatbotsError;

      const chatbotIds = chatbots?.map(bot => bot.id) || [];

      if (chatbotIds.length === 0) {
        setLoading(false);
        return;
      }

      // Fetch messages and sessions
      const [messagesData, sessionsData] = await Promise.all([
        supabase
          .from('messages')
          .select('id, created_at, chat_session_id')
          .in('chatbot_id', chatbotIds),
        supabase
          .from('chat_sessions')
          .select('id, created_at, guest_id, chatbot_id')
          .in('chatbot_id', chatbotIds)
      ]);

      const messages = messagesData.data || [];
      const sessions = sessionsData.data || [];

      // Calculate timeframe dates
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Calculate analytics
      const totalMessages = messages.length;
      const totalSessions = sessions.length;
      const uniqueUsers = new Set(sessions.map(s => s.guest_id).filter(Boolean)).size;
      const totalChatbots = chatbots?.length || 0;

      const weeklyMessages = messages.filter(m => new Date(m.created_at) > weekAgo).length;
      const weeklySessions = sessions.filter(s => new Date(s.created_at) > weekAgo).length;
      const monthlyMessages = messages.filter(m => new Date(m.created_at) > monthAgo).length;
      const monthlySessions = sessions.filter(s => new Date(s.created_at) > monthAgo).length;

      const avgMessagesPerSession = totalSessions > 0 ? totalMessages / totalSessions : 0;

      setAnalytics({
        totalMessages,
        totalSessions,
        uniqueUsers,
        totalChatbots,
        weeklyMessages,
        weeklySessions,
        monthlyMessages,
        monthlySessions,
        avgMessagesPerSession
      });

      // Calculate per-chatbot usage
      const chatbotUsageData: ChatbotUsage[] = chatbots?.map(bot => {
        const botMessages = messages.filter(m => 
          sessions.find(s => s.id === m.chat_session_id)?.chatbot_id === bot.id
        );
        const botSessions = sessions.filter(s => s.chatbot_id === bot.id);
        const botUniqueUsers = new Set(botSessions.map(s => s.guest_id).filter(Boolean)).size;

        return {
          chatbot_id: bot.id,
          chatbot_name: bot.name,
          messages: botMessages.length,
          sessions: botSessions.length,
          unique_users: botUniqueUsers
        };
      }) || [];

      setChatbotUsage(chatbotUsageData);

      // Calculate daily usage for chart
      const days = timeframe === '7d' ? 7 : timeframe === '30d' ? 30 : 90;
      const dailyData: DailyUsage[] = [];

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        const dateStr = date.toISOString().split('T')[0];
        
        const dayMessages = messages.filter(m => 
          m.created_at.startsWith(dateStr)
        ).length;
        
        const daySessions = sessions.filter(s => 
          s.created_at.startsWith(dateStr)
        ).length;

        dailyData.push({
          date: dateStr,
          messages: dayMessages,
          sessions: daySessions
        });
      }

      setDailyUsage(dailyData);

    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportData = () => {
    const csvData = [
      ['Metric', 'Value'],
      ['Total Messages', analytics.totalMessages],
      ['Total Sessions', analytics.totalSessions],
      ['Unique Users', analytics.uniqueUsers],
      ['Total Chatbots', analytics.totalChatbots],
      ['Weekly Messages', analytics.weeklyMessages],
      ['Monthly Messages', analytics.monthlyMessages],
      ['Avg Messages/Session', analytics.avgMessagesPerSession.toFixed(2)],
      [''],
      ['Chatbot', 'Messages', 'Sessions', 'Unique Users'],
      ...chatbotUsage.map(bot => [bot.chatbot_name, bot.messages, bot.sessions, bot.unique_users])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${client?.client_name}-analytics.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex-1 p-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin h-8 w-8 border-b-2 border-blue-600 rounded-full"></div>
        </div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="flex-1 p-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Client Not Found</h1>
          <Link href="/clients">
            <Button>Back to Clients</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-10">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Link href="/clients">
              <Button variant="outline" size="sm" className="mr-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {client.client_name} - Analytics
              </h1>
              <p className="text-gray-600">{client.client_company}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Timeframe Selector */}
            <div className="flex border rounded-lg">
              {(['7d', '30d', '90d'] as const).map((period) => (
                <button
                  key={period}
                  onClick={() => setTimeframe(period)}
                  className={`px-4 py-2 text-sm font-medium ${
                    timeframe === period
                      ? 'bg-blue-500 text-white'
                      : 'text-gray-500 hover:text-gray-700'
                  } ${period === '7d' ? 'rounded-l-lg' : period === '90d' ? 'rounded-r-lg' : ''}`}
                >
                  {period}
                </button>
              ))}
            </div>
            
            <Button onClick={exportData} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <MessageSquare className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Messages</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.totalMessages}</p>
                <p className="text-sm text-gray-500">
                  {timeframe === '7d' ? analytics.weeklyMessages : analytics.monthlyMessages} this {timeframe === '7d' ? 'week' : 'month'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Sessions</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.totalSessions}</p>
                <p className="text-sm text-gray-500">
                  {timeframe === '7d' ? analytics.weeklySessions : analytics.monthlySessions} this {timeframe === '7d' ? 'week' : 'month'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Unique Users</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.uniqueUsers}</p>
                <p className="text-sm text-gray-500">All time</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Bot className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg Msg/Session</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.avgMessagesPerSession.toFixed(1)}
                </p>
                <p className="text-sm text-gray-500">Engagement rate</p>
              </div>
            </div>
          </div>
        </div>

        {/* Chatbot Breakdown */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold">Chatbot Performance</h2>
          </div>
          
          {chatbotUsage.length === 0 ? (
            <div className="p-12 text-center">
              <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No chatbots found</h3>
              <p className="text-gray-500">This client doesn't have any chatbots yet.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Chatbot
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Messages
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sessions
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unique Users
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg Msg/Session
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {chatbotUsage.map((bot) => (
                    <tr key={bot.chatbot_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Bot className="h-5 w-5 text-gray-400 mr-3" />
                          <div className="text-sm font-medium text-gray-900">
                            {bot.chatbot_name}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {bot.messages}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {bot.sessions}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {bot.unique_users}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {bot.sessions > 0 ? (bot.messages / bot.sessions).toFixed(1) : '0.0'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Usage Trend */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold">Usage Trend ({timeframe})</h2>
          </div>
          
          <div className="p-6">
            {dailyUsage.length > 0 ? (
              <div className="space-y-4">
                <div className="grid grid-cols-7 gap-2 text-xs text-gray-500">
                  {dailyUsage.slice(-7).map((day, index) => (
                    <div key={index} className="text-center">
                      {new Date(day.date).toLocaleDateString('en', { weekday: 'short' })}
                    </div>
                  ))}
                </div>
                <div className="grid grid-cols-7 gap-2">
                  {dailyUsage.slice(-7).map((day, index) => (
                    <div key={index} className="text-center">
                      <div className="bg-blue-100 rounded p-2 mb-1">
                        <div className="text-sm font-bold text-blue-800">{day.messages}</div>
                        <div className="text-xs text-blue-600">msgs</div>
                      </div>
                      <div className="bg-green-100 rounded p-2">
                        <div className="text-sm font-bold text-green-800">{day.sessions}</div>
                        <div className="text-xs text-green-600">sessions</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No usage data available for this period</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
