'use client';
import { <PERSON><PERSON> } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "../../../../components/ui/dialog";
import { useEffect, useState, useCallback } from "react";
import { Label } from '../../../../components/ui/label';
import Avatar from "../../../../components/Avatar";
import { Message, ChatSession } from "../../../../types/types";
import Messages from "../../../../components/Messages";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage
} from "../../../../components/ui/form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../../../components/ui/dropdown-menu";
import { useForm } from "react-hook-form";
import { supabase } from '../../../../supabaseClient';
import { Loader2, MessageSquare, Plus, History, Send, LogOut, Trash2 } from "lucide-react";
import { toast } from "sonner";
const formSchema = z.object({
  message: z.string().min(1, "Message cannot be empty").max(1000, "Message is too long"),
});

const userInfoSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(50, "Name is too long"),
  email: z.string().email("Please enter a valid email address"),
});

interface ChatbotPageProps {
  params: {
    id: string;
  };
}

function ChatbotPage({ params }: ChatbotPageProps) {
  const { id } = params;
  const chatbotId = parseInt(id);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [isOpen, setIsOpen] = useState(true);
  const [chatId, setChatId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatbot, setChatbot] = useState<any>(null);
  const [userSessions, setUserSessions] = useState<ChatSession[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      message: "",
    },
  });

  // Fetch all chat sessions for a user
  const fetchUserSessions = useCallback(async (userEmail: string) => {
    if (!userEmail) return;
    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .select('*')
        .eq('chatbot_id', id)
        .eq('email', userEmail)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUserSessions(data || []);
    } catch (error) {
      console.error("Error fetching user sessions:", error);
      setError("Failed to load chat history");
    }
  }, [id]);


  // Check for existing chat session
  const checkExistingSession = async (userEmail: string) => {
    const { data, error } = await supabase
      .from('chat_sessions')
      .select('*')
      .eq('chatbot_id', id)
      .eq('email', userEmail)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.log("No existing session found, creating a new one.");
    }

    if (data) {
      setChatId(data.id);
      return true;
    }

    return false;
  };

  // Fetch chatbot data
  const fetchChatbot = useCallback(async () => {
    try {
      setIsInitializing(true);
      const { data, error } = await supabase
        .from('chatbots')
        .select(`
          *,
          chatbot_characteristics (
            content
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      if (!data) throw new Error('Chatbot not found');

      setChatbot(data);
      setError(null);
    } catch (error) {
      console.error("Error fetching chatbot:", error);
      setError("Failed to load chatbot. Please refresh the page.");
    } finally {
      setIsInitializing(false);
    }
  }, [id]);

  // Fetch messages
  const fetchMessages = useCallback(async () => {
    if (!chatId) {
      setMessages([]);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('chat_session_id', chatId)
        .order('created_at', { ascending: true });

      if (error) throw error;
      setMessages(data || []);
    } catch (error) {
      console.error("Error fetching messages:", error);
      toast("Failed to load messages");
    }
  }, [chatId]);

  // Initial data fetch
  useEffect(() => {
    fetchChatbot();
  }, [id]);

  // Fetch messages when chatId changes
  useEffect(() => {
    fetchMessages();
  }, [chatId]);

  // Supabase real-time subscription
  useEffect(() => {
    if (!chatId) return;

    const channel = supabase
      .channel(`chat-channel-${chatId}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events
          schema: 'public',
          table: 'messages',
          filter: `chat_session_id=eq.${chatId}`,
        },
        async () => {
          await fetchMessages();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [chatId]);

  const handleInformationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate user input
    const validation = userInfoSchema.safeParse({ name, email });
    if (!validation.success) {
      const errors = validation.error.errors.map(err => err.message).join(', ');
      toast(errors);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Fetch all past sessions for the dropdown first
      await fetchUserSessions(email);

      // Check for an active session to load
      const hasExistingSession = await checkExistingSession(email);

      if (!hasExistingSession) {
        // This is a new user or a user starting a truly new session
        // Insert guest info (if not exists)
        const { error: guestError } = await supabase
          .from('guests')
          .insert([{ name, email }])
          .select()
          .single();

        if (guestError && guestError.code !== '23505') { // Ignore unique constraint violations
          console.error("Guest insert error:", guestError);
          throw guestError;
        }

        // Create a new chat session to start with
        await handleNewChat(true); // Pass flag to avoid refetching sessions
      }

      setIsOpen(false);
      toast("Welcome! Your chat session is ready.");
    } catch (error) {
      console.error("Session creation error:", error);
      setError("An error occurred while creating your session. Please try again.");
      toast("Failed to create session. Please try again.");
    } finally {
      setLoading(false);
    }
  };


  // NEW: Function to handle creating a new chat
  const handleNewChat = async (isInitial = false) => {
    setLoading(true);
    setMessages([]); // Clear messages immediately for better UX
    setChatId(null);
  
    try {
      const { data: newChatSession, error: chatError } = await supabase
        .from('chat_sessions')
        .insert([{
          chatbot_id: chatbotId,
          name,
          email,
          status: 'active'
        }])
        .select()
        .single();
  
      if (chatError) throw chatError;
  
      setChatId(newChatSession.id);
  
      // If this is not the very first chat session being created, refresh the sessions list
      if (!isInitial) {
        await fetchUserSessions(email);
      }
  
    } catch (error) {
      console.error("Error creating new chat:", error);
      alert("Could not create a new chat session.");
    } finally {
      setLoading(false);
    }
  };
  
  // NEW: Function to switch to an old chat
  const handleSelectChat = (sessionId: number) => {
    setChatId(sessionId);
  };

  // Function to handle logout
  const handleLogout = () => {
    setIsOpen(true);
    setName("");
    setEmail("");
    setChatId(null);
    setMessages([]);
    setUserSessions([]);
    setError(null);
    toast("Logged out successfully");
  };

  // Function to delete a chat session
  const handleDeleteChat = async (sessionId: number, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent dropdown from closing

    try {
      // Delete all messages in the chat session first
      const { error: messagesError } = await supabase
        .from('messages')
        .delete()
        .eq('chat_session_id', sessionId);

      if (messagesError) throw messagesError;

      // Delete the chat session
      const { error: sessionError } = await supabase
        .from('chat_sessions')
        .delete()
        .eq('id', sessionId);

      if (sessionError) throw sessionError;

      // If the deleted session was the current one, clear the current chat
      if (chatId === sessionId) {
        setChatId(null);
        setMessages([]);
      }

      // Refresh the user sessions list
      await fetchUserSessions(email);

      toast("Chat deleted successfully");
    } catch (error) {
      console.error("Error deleting chat:", error);
      toast("Failed to delete chat. Please try again.");
    }
  };

  // Function to delete all chat sessions for the user
  const handleDeleteAllChats = async () => {
    if (!email) return;

    // Show confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to delete all ${userSessions.length} chat sessions? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      // Get all user sessions for this chatbot
      const userSessionIds = userSessions.map(session => session.id);

      if (userSessionIds.length === 0) {
        toast("No chats to delete");
        return;
      }

      // Delete all messages for these sessions
      const { error: messagesError } = await supabase
        .from('messages')
        .delete()
        .in('chat_session_id', userSessionIds);

      if (messagesError) throw messagesError;

      // Delete all chat sessions
      const { error: sessionsError } = await supabase
        .from('chat_sessions')
        .delete()
        .eq('chatbot_id', id)
        .eq('email', email);

      if (sessionsError) throw sessionsError;

      // Clear current chat
      setChatId(null);
      setMessages([]);
      setUserSessions([]);

      toast("All chats deleted successfully");
    } catch (error) {
      console.error("Error deleting all chats:", error);
      toast("Failed to delete all chats. Please try again.");
    }
  };


  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!name || !email || chatId === null) {
      setIsOpen(true);
      return;
    }

    const message = values.message.trim();
    if (!message) return;

    setLoading(true);
    form.reset();

    // Create and display user message
    const userMessage: Message = {
      id: Date.now(),
      content: message,
      created_at: new Date().toISOString(),
      chat_session_id: chatId,
      sender: "user",
      role: "user"
    };

    // Create and display loading message
    const loadingMessage: Message = {
      id: Date.now() + 1,
      content: "Thinking...",
      created_at: new Date().toISOString(),
      chat_session_id: chatId,
      sender: "ai",
      role: "assistant"
    };

    setMessages(prevMessages => [...prevMessages, userMessage, loadingMessage]);

    try {
      // Save user message
      const { error: messageError } = await supabase
        .from('messages')
        .insert([{
          chat_session_id: chatId,
          content: message,
          sender: "user"
        }]);

      if (messageError) throw messageError;

      // Get chatbot characteristics
      const systemPrompt = chatbot?.chatbot_characteristics
        ?.map((c: any) => c.content)
        .join(" ") || "No specific instructions.";

      // Prepare messages for AI
      const allMessages = messages.map(msg => ({
        role: msg.sender === "ai" ? "assistant" : "user",
        content: msg.content || ""
      })) as Array<{ role: "assistant" | "user", content: string }>;

      // Add the current user message
      const messagesForAI = [...allMessages, { role: "user", content: message }];

      // Get AI response from our API route
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: messagesForAI,
          systemPrompt,
          name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const { response: aiResponse } = await response.json();

      if (aiResponse) {
        // Save AI response
        const { error: aiMessageError } = await supabase
          .from('messages')
          .insert([{
            chat_session_id: chatId,
            content: aiResponse,
            sender: "ai"
          }]);

        if (aiMessageError) throw aiMessageError;

        // Update UI immediately
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            msg.id === loadingMessage.id
              ? { ...msg, content: aiResponse }
              : msg
          )
        );
      }

      // Fetch latest messages
      await fetchMessages();

    } catch (error) {
      console.error("Error sending message:", error);
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === loadingMessage.id
            ? { ...msg, content: "❌ Sorry, I couldn't process your message. Please try again." }
            : msg
        )
      );
      toast("Failed to send message. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Show loading state while initializing
  if (isInitializing) {
    return (
      <div className="w-full flex bg-gray-100 min-h-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600">Loading chatbot...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error && !chatbot) {
    return (
      <div className="w-full flex bg-gray-100 min-h-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4 text-center max-w-md">
          <MessageSquare className="h-12 w-12 text-gray-400" />
          <h2 className="text-xl font-semibold text-gray-800">Oops! Something went wrong</h2>
          <p className="text-gray-600">{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full flex bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <form onSubmit={handleInformationSubmit}>
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-blue-600" />
                <span>Let's Help You Out</span>
              </DialogTitle>
              <DialogDescription>
                I just need a few details to get started with your chat session.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right font-medium">
                  Name
                </Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="John Doe"
                  className="col-span-3"
                  disabled={loading}
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right font-medium">
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="col-span-3"
                  disabled={loading}
                  required
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="submit" disabled={!name || !email || loading} className="w-full">
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting up your chat...
                  </>
                ) : (
                  <>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Start Chatting
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col w-full max-w-4xl mx-auto bg-white md:rounded-t-lg shadow-2xl md:mt-10 min-h-[80vh] md:min-h-[600px]">
        <div className="sticky top-0 z-50 bg-gradient-to-r from-[#4D7DFB] to-[#2991EE] py-5 px-6 text-white md:rounded-t-lg flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Avatar
                seed={chatbot?.name}
                className="h-12 w-12 bg-white rounded-full border-2 border-white shadow-lg"
              />
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
            </div>
            <div>
              <h1 className="truncate text-lg font-semibold">{chatbot?.name}</h1>
              <p className="text-sm text-blue-100 flex items-center">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                Online • Typically replies instantly
              </p>
            </div>
          </div>

          {/* Chat management buttons */}
          {!isOpen && (
            <div className="flex items-center space-x-2">
              {/* User avatar with logout */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-10 h-10 rounded-full bg-white text-blue-600 hover:bg-blue-50 border-white p-0 font-semibold"
                  >
                    B
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-48" align="end">
                  <DropdownMenuLabel className="flex flex-col">
                    <span className="font-medium">{name}</span>
                    <span className="text-xs text-gray-500 font-normal">{email}</span>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="cursor-pointer text-red-600 focus:text-red-600"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Logout</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="text-blue-600 bg-white hover:bg-blue-50 border-white">
                    <History className="mr-2 h-4 w-4" />
                    <span className="hidden sm:inline">Chat History</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-64" align="end">
                  <DropdownMenuLabel className="flex items-center justify-between">
                    <div className="flex items-center">
                      <History className="mr-2 h-4 w-4" />
                      Your Chat History
                    </div>
                    {userSessions.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                        onClick={handleDeleteAllChats}
                        title="Delete all chats"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {(() => {
                    // Get all sessions EXCEPT the one currently active
                    const pastSessions = userSessions.filter(session => session.id !== chatId);
                    // Sort the past sessions to show the most recent ones first
                    const sortedSessions = pastSessions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

                    if (sortedSessions.length > 0) {
                      return sortedSessions.slice(0, 5).map((session) => (
                        <DropdownMenuItem
                          key={session.id}
                          className="cursor-pointer p-0"
                        >
                          <div className="flex items-center justify-between w-full p-2">
                            <div
                              className="flex flex-col flex-1 cursor-pointer"
                              onClick={() => handleSelectChat(session.id)}
                            >
                              <span className="font-medium">Chat Session</span>
                              <span className="text-xs text-gray-500">
                                {new Date(session.created_at).toLocaleDateString()} at {new Date(session.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={(e) => handleDeleteChat(session.id, e)}
                              title="Delete chat"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </DropdownMenuItem>
                      ));
                    } else {
                      return (
                        <DropdownMenuItem disabled className="text-center text-gray-500">
                          No past chats found
                        </DropdownMenuItem>
                      );
                    }
                  })()}
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                onClick={() => handleNewChat()}
                className="bg-white text-blue-600 hover:bg-blue-50 border border-white"
                disabled={loading}
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    <span className="hidden sm:inline">New Chat</span>
                  </>
                )}
              </Button>
            </div>
          )}
        </div>

        <Messages
          messages={messages}
          chatbotName={chatbot?.name}
          chatbotId={id}
        />

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex items-end sticky bottom-0 z-50 space-x-3 drop-shadow-lg p-4 bg-white border-t border-gray-200"
          >
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <div className="relative">
                      <Input
                        placeholder="Type your message..."
                        {...field}
                        className="pr-12 py-3 text-base border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
                        disabled={loading}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            form.handleSubmit(onSubmit)();
                          }
                        }}
                      />
                      {field.value && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">
                          {field.value.length}/1000
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage className="text-xs mt-1" />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              disabled={loading || !form.watch('message')?.trim()}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}

export default ChatbotPage;