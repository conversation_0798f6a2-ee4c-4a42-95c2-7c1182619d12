// import Stripe from 'stripe';

// export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
//   apiVersion: '2024-10-28.acacia',
// });

// export const createStripeCustomer = async (email: string) => {
//   const customer = await stripe.customers.create({
//     email,
//   });
//   return customer.id;
// };

// export const createSubscription = async (
//   customerId: string,
//   priceId: string
// ) => {
//   return await stripe.subscriptions.create({
//     customer: customerId,
//     items: [{ price: priceId }],
//     payment_behavior: 'default_incomplete',
//     expand: ['latest_invoice.payment_intent'],
//   });
// }; 