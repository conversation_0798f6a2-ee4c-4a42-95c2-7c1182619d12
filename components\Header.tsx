'use client';

import Link from 'next/link'
import React, { useState } from 'react'
import Avatar from './Avatar'
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs'
import LanguageSelector from './LanguageSelector'

function Header() {
  const [currentLanguage, setCurrentLanguage] = useState('en');

  const handleLanguageChange = (language: string) => {
    setCurrentLanguage(language);
    // Store language preference in localStorage for future use
    if (typeof window !== 'undefined') {
      localStorage.setItem('saffabot-language', language);
    }
    console.log('Language changed to:', language);
  };

  return (
    <header className="p-5 bg-white shadow-sm text-gray-800 flex justify-between items-center">
      <Link
        href='/'
        className="flex items-center text-4xl font-thin space-x-3 hover:opacity-90 transition-opacity"
      >
        <Avatar
          seed="SaffaBot Support Agent"
          className="space-y-1" // Added consistent sizing
        />

        <div className="flex flex-col">
          <h1 className="font-bold text-xl">SaffaBot</h1>
          <h2 className="text-sm text-gray-600">Chat the Saffa way</h2>
        </div>
      </Link>

      <div className="flex items-center space-x-4">
        <SignedIn>
          {/* Language Selector for signed-in users */}
          <LanguageSelector
            onLanguageChange={handleLanguageChange}
            currentLanguage={currentLanguage}
            variant="compact"
          />
          <UserButton showName />
        </SignedIn>

        <SignedOut>
          <SignInButton />
        </SignedOut>
      </div>
    </header>
  )
}

export default Header;
