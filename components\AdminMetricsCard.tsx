import { LucideIcon } from "lucide-react"

interface AdminMetricsCardProps {
  title: string
  value: string | number
  icon: LucideIcon
}

export default function AdminMetricsCard({ title, value, icon: Icon }: AdminMetricsCardProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-gray-500 text-sm font-medium">{title}</h3>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
        </div>
        <div className="bg-[#64B5F5] bg-opacity-10 p-3 rounded-lg">
          <Icon className="h-6 w-6 text-[#64B5F5]" />
        </div>
      </div>
    </div>
  )
}