// "use client";

// import { useState, useEffect } from 'react';
// import { supabase } from '../supabaseClient';
// import dynamic from 'next/dynamic';

// const ArticleEditor = dynamic(() => import('./ArticleEditor'), { ssr: false });
// const ArticleViewer = dynamic(() => import('./ArticleViewer'), { ssr: false });

// export default function KnowledgeBaseManager({ botId }: { botId: string }) {
//   const [articles, setArticles] = useState<any[]>([]);
//   const [selectedArticle, setSelectedArticle] = useState<any>(null);
//   const [isEditing, setIsEditing] = useState(false);

//   useEffect(() => {
//     fetchArticles();
//   }, [botId]);

//   const fetchArticles = async () => {
//     const { data, error } = await supabase
//       .from('knowledge_base')
//       .select('*')
//       .eq('chatbot_id', botId);

//     if (data) {
//       setArticles(data);
//     }
//   };

//   const saveArticle = async (articleData: any) => {
//     if (selectedArticle) {
//       // Update existing article
//       const { data, error } = await supabase
//         .from('knowledge_base')
//         .update(articleData)
//         .eq('id', selectedArticle.id);
//     } else {
//       // Create new article
//       const { data, error } = await supabase
//         .from('knowledge_base')
//         .insert([{ ...articleData, chatbot_id: botId }]);
//     }

//     fetchArticles();
//     setIsEditing(false);
//   };

//   return (
//     <div className="grid grid-cols-12 gap-6">
//       {/* Article List Sidebar */}
//       <div className="col-span-3 bg-white rounded-lg shadow p-4">
//         <div className="flex justify-between items-center mb-4">
//           <h2 className="text-lg font-bold">Knowledge Base</h2>
//           <button
//             onClick={() => {
//               setSelectedArticle(null);
//               setIsEditing(true);
//             }}
//             className="px-4 py-2 bg-blue-500 text-white rounded-lg"
//           >
//             Add Article
//           </button>
//         </div>

//         <div className="space-y-2">
//           {articles.map((article) => (
//             <div
//               key={article.id}
//               className={`p-3 rounded-lg cursor-pointer ${
//                 selectedArticle?.id === article.id ? 'bg-blue-50' : 'hover:bg-gray-50'
//               }`}
//               onClick={() => setSelectedArticle(article)}
//             >
//               <h3 className="font-medium">{article.title}</h3>
//               <p className="text-sm text-gray-500">{article.category}</p>
//             </div>
//           ))}
//         </div>
//       </div>

//       {/* Article Editor */}
//       <div className="col-span-9 bg-white rounded-lg shadow p-6">
//         {isEditing ? (
//           <ArticleEditor
//             article={selectedArticle}
//             onSave={saveArticle}
//             onCancel={() => setIsEditing(false)}
//           />
//         ) : selectedArticle ? (
//           <ArticleViewer
//             article={selectedArticle}
//             onEdit={() => setIsEditing(true)}
//           />
//         ) : (
//           <div className="text-center text-gray-500">
//             Select an article to view or create a new one
//           </div>
//         )}
//       </div>
//     </div>
//   );
// } 