import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { auth } from '@clerk/nextjs/server';

export default async function TestReviewSessions() {
  const { userId } = await auth();
  
  if (!userId) {
    return (
      <div className="flex-1 p-10">
        <h1 className="text-2xl font-bold mb-4">Review Sessions Test</h1>
        <div className="bg-yellow-50 text-yellow-600 p-4 rounded-lg">
          Please log in to test review sessions.
        </div>
      </div>
    );
  }

  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  try {
    // Test 1: Fetch user's chatbots
    const { data: chatbots, error: chatbotsError } = await supabase
      .from('chatbots')
      .select('id, name, clerk_user_id, created_at')
      .eq('clerk_user_id', userId);

    // Test 2: Fetch chat sessions
    const { data: sessions, error: sessionsError } = await supabase
      .from('chat_sessions')
      .select(`
        id,
        created_at,
        name,
        email,
        guest_id,
        status,
        chatbot_id
      `)
      .in('chatbot_id', chatbots?.map(bot => bot.id) || []);

    // Test 3: Fetch messages
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('id, content, role, created_at, chatbot_id')
      .in('chatbot_id', chatbots?.map(bot => bot.id) || []);

    // Test 4: Fetch guests
    const { data: guests, error: guestsError } = await supabase
      .from('guests')
      .select('id, name, email, created_at');

    return (
      <div className="flex-1 p-10">
        <h1 className="text-2xl font-bold mb-6">Review Sessions Test</h1>
        
        <div className="space-y-6">
          {/* User Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h2 className="font-semibold mb-2">User Info</h2>
            <p>User ID: {userId}</p>
          </div>

          {/* Chatbots Test */}
          <div className="bg-white border rounded-lg p-4">
            <h2 className="font-semibold mb-2">Chatbots ({chatbots?.length || 0})</h2>
            {chatbotsError && (
              <div className="bg-red-50 text-red-600 p-2 rounded mb-2">
                Error: {chatbotsError.message}
              </div>
            )}
            {chatbots?.length === 0 ? (
              <p className="text-gray-500">No chatbots found</p>
            ) : (
              <ul className="space-y-1">
                {chatbots?.map(bot => (
                  <li key={bot.id} className="text-sm">
                    {bot.name} (ID: {bot.id})
                  </li>
                ))}
              </ul>
            )}
          </div>

          {/* Sessions Test */}
          <div className="bg-white border rounded-lg p-4">
            <h2 className="font-semibold mb-2">Chat Sessions ({sessions?.length || 0})</h2>
            {sessionsError && (
              <div className="bg-red-50 text-red-600 p-2 rounded mb-2">
                Error: {sessionsError.message}
              </div>
            )}
            {sessions?.length === 0 ? (
              <p className="text-gray-500">No chat sessions found</p>
            ) : (
              <ul className="space-y-1">
                {sessions?.slice(0, 5).map(session => (
                  <li key={session.id} className="text-sm">
                    Session {session.id} - {session.name || 'Anonymous'} 
                    ({new Date(session.created_at).toLocaleDateString()})
                  </li>
                ))}
                {sessions && sessions.length > 5 && (
                  <li className="text-sm text-gray-500">...and {sessions.length - 5} more</li>
                )}
              </ul>
            )}
          </div>

          {/* Messages Test */}
          <div className="bg-white border rounded-lg p-4">
            <h2 className="font-semibold mb-2">Messages ({messages?.length || 0})</h2>
            {messagesError && (
              <div className="bg-red-50 text-red-600 p-2 rounded mb-2">
                Error: {messagesError.message}
              </div>
            )}
            {messages?.length === 0 ? (
              <p className="text-gray-500">No messages found</p>
            ) : (
              <ul className="space-y-1">
                {messages?.slice(0, 3).map(message => (
                  <li key={message.id} className="text-sm">
                    {message.role}: {message.content?.substring(0, 50)}...
                  </li>
                ))}
                {messages && messages.length > 3 && (
                  <li className="text-sm text-gray-500">...and {messages.length - 3} more</li>
                )}
              </ul>
            )}
          </div>

          {/* Guests Test */}
          <div className="bg-white border rounded-lg p-4">
            <h2 className="font-semibold mb-2">Guests ({guests?.length || 0})</h2>
            {guestsError && (
              <div className="bg-red-50 text-red-600 p-2 rounded mb-2">
                Error: {guestsError.message}
              </div>
            )}
            {guests?.length === 0 ? (
              <p className="text-gray-500">No guests found</p>
            ) : (
              <ul className="space-y-1">
                {guests?.slice(0, 5).map(guest => (
                  <li key={guest.id} className="text-sm">
                    {guest.name || 'No name'} - {guest.email || 'No email'}
                  </li>
                ))}
                {guests && guests.length > 5 && (
                  <li className="text-sm text-gray-500">...and {guests.length - 5} more</li>
                )}
              </ul>
            )}
          </div>

          {/* Navigation Test */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h2 className="font-semibold mb-2">Navigation Test</h2>
            <div className="space-x-4">
              <a href="/review-sessions" className="text-blue-600 hover:underline">
                Go to Review Sessions
              </a>
              <a href="/view-chatbots" className="text-blue-600 hover:underline">
                Go to View Chatbots
              </a>
              <a href="/create-chatbot" className="text-blue-600 hover:underline">
                Create New Chatbot
              </a>
            </div>
          </div>
        </div>
      </div>
    );

  } catch (error) {
    return (
      <div className="flex-1 p-10">
        <h1 className="text-2xl font-bold mb-4">Review Sessions Test</h1>
        <div className="bg-red-50 text-red-600 p-4 rounded-lg">
          Unexpected error: {error instanceof Error ? error.message : 'Unknown error'}
        </div>
      </div>
    );
  }
}

export const dynamic = "force-dynamic";
