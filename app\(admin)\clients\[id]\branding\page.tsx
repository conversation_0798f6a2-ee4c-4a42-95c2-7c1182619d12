'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useParams, useRouter } from 'next/navigation';
import { supabase } from '../../../../../supabaseClient';
import { Button } from '../../../../../components/ui/button';
import { Input } from '../../../../../components/ui/input';
import { ArrowLeft, Upload, Eye, Save } from 'lucide-react';
import Link from 'next/link';

interface Client {
  id: number;
  client_name: string;
  client_company: string;
  brand_primary_color: string;
  brand_secondary_color: string;
}

interface ClientBranding {
  id?: number;
  client_id: number;
  logo_url?: string;
  primary_color: string;
  secondary_color: string;
  font_family: string;
  welcome_message?: string;
  footer_text?: string;
  custom_css?: string;
}

export default function ClientBrandingPage() {
  const { user } = useUser();
  const params = useParams();
  const router = useRouter();
  const clientId = Number(params.id);

  const [client, setClient] = useState<Client | null>(null);
  const [branding, setBranding] = useState<ClientBranding>({
    client_id: clientId,
    primary_color: '#4D7DFB',
    secondary_color: '#FFCC00',
    font_family: 'Inter',
    welcome_message: 'Hello! How can I help you today?',
    footer_text: 'Powered by SaffaBot'
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user && clientId) {
      fetchClientAndBranding();
    }
  }, [user, clientId]);

  const fetchClientAndBranding = async () => {
    if (!user) return;

    try {
      // Fetch client details
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('*')
        .eq('id', clientId)
        .eq('it_business_user_id', user.id)
        .single();

      if (clientError) throw clientError;
      setClient(clientData);

      // Fetch existing branding
      const { data: brandingData, error: brandingError } = await supabase
        .from('client_branding')
        .select('*')
        .eq('client_id', clientId)
        .single();

      if (brandingData) {
        setBranding(brandingData);
      } else if (brandingError && brandingError.code !== 'PGRST116') {
        throw brandingError;
      }

    } catch (error) {
      console.error('Error fetching data:', error);
      alert('Failed to load client data');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user || !client) return;

    setSaving(true);
    try {
      if (branding.id) {
        // Update existing branding
        const { error } = await supabase
          .from('client_branding')
          .update(branding)
          .eq('id', branding.id);

        if (error) throw error;
      } else {
        // Create new branding
        const { data, error } = await supabase
          .from('client_branding')
          .insert([branding])
          .select()
          .single();

        if (error) throw error;
        setBranding(data);
      }

      // Also update client table with basic colors
      await supabase
        .from('clients')
        .update({
          brand_primary_color: branding.primary_color,
          brand_secondary_color: branding.secondary_color
        })
        .eq('id', clientId);

      alert('Branding saved successfully!');
    } catch (error) {
      console.error('Error saving branding:', error);
      alert('Failed to save branding');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex-1 p-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin h-8 w-8 border-b-2 border-blue-600 rounded-full"></div>
        </div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="flex-1 p-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Client Not Found</h1>
          <Link href="/clients">
            <Button>Back to Clients</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-10">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Link href="/clients">
            <Button variant="outline" size="sm" className="mr-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {client.client_name} - Branding
            </h1>
            <p className="text-gray-600">{client.client_company}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Branding Form */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-bold mb-4">Brand Colors</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Primary Color
                  </label>
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      value={branding.primary_color}
                      onChange={(e) => setBranding({...branding, primary_color: e.target.value})}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <Input
                      value={branding.primary_color}
                      onChange={(e) => setBranding({...branding, primary_color: e.target.value})}
                      placeholder="#4D7DFB"
                      className="flex-1"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Secondary Color
                  </label>
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      value={branding.secondary_color}
                      onChange={(e) => setBranding({...branding, secondary_color: e.target.value})}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <Input
                      value={branding.secondary_color}
                      onChange={(e) => setBranding({...branding, secondary_color: e.target.value})}
                      placeholder="#FFCC00"
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-bold mb-4">Messages</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Welcome Message
                  </label>
                  <Input
                    value={branding.welcome_message || ''}
                    onChange={(e) => setBranding({...branding, welcome_message: e.target.value})}
                    placeholder="Hello! How can I help you today?"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Footer Text
                  </label>
                  <Input
                    value={branding.footer_text || ''}
                    onChange={(e) => setBranding({...branding, footer_text: e.target.value})}
                    placeholder="Powered by SaffaBot"
                  />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-bold mb-4">Logo Upload</h2>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-2">Upload client logo</p>
                <p className="text-sm text-gray-400">PNG, JPG up to 2MB</p>
                <Button variant="outline" className="mt-4">
                  Choose File
                </Button>
              </div>
            </div>

            <div className="flex space-x-4">
              <Button onClick={handleSave} disabled={saving} className="flex-1">
                {saving ? (
                  <div className="animate-spin h-4 w-4 border-b-2 border-white rounded-full mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save Branding
              </Button>
              <Button variant="outline">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
            </div>
          </div>

          {/* Preview */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-4">Live Preview</h2>
            
            {/* Chatbot Preview */}
            <div className="border rounded-lg overflow-hidden">
              {/* Header */}
              <div 
                className="p-4 text-white"
                style={{ backgroundColor: branding.primary_color }}
              >
                <h3 className="font-bold">{client.client_name} Support</h3>
                <p className="text-sm opacity-90">Online now</p>
              </div>
              
              {/* Messages */}
              <div className="p-4 space-y-3 bg-gray-50 min-h-[200px]">
                <div className="flex justify-start">
                  <div 
                    className="max-w-xs px-4 py-2 rounded-lg text-white"
                    style={{ backgroundColor: branding.primary_color }}
                  >
                    {branding.welcome_message || 'Hello! How can I help you today?'}
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <div className="max-w-xs px-4 py-2 rounded-lg bg-white border">
                    Hi, I need help with my account
                  </div>
                </div>
              </div>
              
              {/* Footer */}
              <div className="p-2 text-center text-xs text-gray-500 border-t">
                {branding.footer_text || 'Powered by SaffaBot'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
