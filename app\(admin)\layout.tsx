import Header from '../../components/Header';  // Import Header component
import Sidebar from '../../components/Sidebar';  // Import Sidebar component
import { auth } from '@clerk/nextjs/server';  // Import Clerk's auth function
import { redirect } from 'next/navigation';  // Import Next.js redirect function

// AdminLayout is a wrapper that checks if the user is logged in and renders the content accordingly
async function AdminLayout({ children }: { children: React.ReactNode }) {
  // Retrieve the userId from the auth function (Clerk)
  const { userId } = await auth();
  
  // If userId is not available (i.e., the user is not authenticated), redirect them to the login page
  if (!userId) {
    redirect('/login');
  }

  // The actual layout structure of the admin panel page
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header component */}
      <Header />
      
      {/* Main content area: flex column for mobile and flex row for larger screens */}
      <div className="flex-1 flex flex-col lg:flex-row bg-gray-100">
        
        {/* Sidebar component */}
        <Sidebar />
        
        {/* Main content area where children will be displayed */}
        <div className="flex-1 flex justify-center lg:justify-start items-start max-w-5xl mx-auto w-full">
          {children}  {/* Display the child components passed to this layout */}
        </div>
      </div>
    </div>
  );
}

export default AdminLayout;
