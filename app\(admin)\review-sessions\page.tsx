import { Suspense } from 'react';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { auth } from '@clerk/nextjs/server';
import { Chatbot } from '../../../types/types';
import { default as dynamicImport } from 'next/dynamic';

// Define Props type if needed
type Props = {
  params: { [key: string]: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

// Dynamic import of ChatBotSessions
const ChatBotSessions = dynamicImport(
  () => import('../../../components/ChatBotSessions'),
  { ssr: true }
);

// Rename to Page and add Props type
export default async function Page({ 
  params,
  searchParams 
}: {
  params: { [key: string]: string };
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  // Auth check to ensure the user is logged in
  const { userId } = await auth();
  if (!userId) {
    return (
      <div className="flex-1 p-10">
        <div className="bg-yellow-50 text-yellow-600 p-4 rounded-lg">
          Please log in to view your chat sessions.
        </div>
      </div>
    );
  }

  // Create Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );
    

  // Fetch chatbots with their sessions and guest information
  const { data: chatbots, error } = await supabase
    .from('chatbots')
    .select(`
      id, 
      name, 
      clerk_user_id,
      created_at,
      chat_sessions (
        id, 
        created_at,
        name,
        email,
        guest_id,
        status,
        guest:guests (
          id,
          name,
          email,
          created_at
        )
      )
    `)
    .eq('clerk_user_id', userId);

  // Add debugging - you can uncomment these lines to see what data is being fetched
  // console.log('Fetched chatbots:', chatbots);
  // console.log('User ID:', userId);

  // Error handling if Supabase query fails
  if (error) {
    console.error('Supabase error:', error);
 
    return (
      <div className="flex-1 p-10">
        <div className="bg-red-50 text-red-500 p-4 rounded-lg">
          Error loading chat sessions: {error.message || 'Please try again later.'}
        </div>
      </div>
    );
  }

  // Process and sort chatbots and sessions
  const sortedChatbots: Chatbot[] = (chatbots || []).map((chatbot) => ({
    ...chatbot,
    id: Number(chatbot.id),
    status: 'active' as const,
    chatbot_characteristics: [],
    chat_sessions: (chatbot.chat_sessions || []).map((session) => ({
      ...session,
      id: Number(session.id),
      guest_id: session.guest_id ? Number(session.guest_id) : null,
      status: session.status || 'active',
      email: session.email || '',
      name: session.name || '',
      user_id: userId,
      messages: [],
      // Fix guest data structure - guest is an array, so handle it properly
      guests: session.guest && Array.isArray(session.guest) && session.guest.length > 0 ? {
        id: Number(session.guest[0].id),
        name: session.guest[0].name || '',
        email: session.guest[0].email || '',
        created_at: session.guest[0].created_at
      } : null,
      chatbots: {
        length: 1,
        name: chatbot.name,
        guest: session.guest
      }
    })),
  }));

  // Check if there are any chatbots with sessions
  const hasAnySessions = sortedChatbots.some(chatbot => chatbot.chat_sessions.length > 0);

  // Return UI to render chatbots and their sessions
  return (
    <div className="flex-1 px-10">
      <h1 className="text-xl lg:text-3xl font-semibold mt-10">Chat Sessions</h1>
      <h2 className="mb-5">
        Review all the chat sessions the chatbots have had with your customers.
      </h2>

      {!hasAnySessions ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Chat Sessions Yet</h3>
          <p className="text-gray-500 mb-4">
            Your chatbots haven't had any conversations yet. Once customers start chatting, you'll see their sessions here.
          </p>
          <div className="text-sm text-gray-400">
            Make sure your chatbots are embedded on your website or share the chatbot links with your customers.
          </div>
        </div>
      ) : (
        <Suspense fallback={
          <div className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              <div className="h-8 bg-gray-300 rounded"></div>
              <div className="h-8 bg-gray-300 rounded"></div>
              <div className="h-8 bg-gray-300 rounded"></div>
            </div>
          </div>
        }>
          <ChatBotSessions chatbots={sortedChatbots} />
        </Suspense>
      )}
    </div>
  );
}

// Add metadata if needed
export const metadata = {
  title: 'Review Sessions',
  description: 'Review all chat sessions',
}

// Add dynamic configuration if needed
export const dynamic = 'force-dynamic'
