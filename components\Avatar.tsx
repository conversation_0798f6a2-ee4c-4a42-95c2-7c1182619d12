import React from 'react';
import { createAvatar } from '@dicebear/core';
import { bottts } from '@dicebear/collection';
import Image from 'next/image';

interface AvatarProps {
  seed: string;
  className?: string;
}

function Avatar({ seed, className }: AvatarProps) {
  // Create the avatar
  const avatar = createAvatar(bottts, {
    seed,
    size: 100 // Match the img dimensions
  });

  // Convert the avatar to SVG
  const svg = avatar.toString();

  // Encode the SVG to Base64
  const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;

  return (
    <Image
      src={dataUrl}
      alt="User Avatar"
      width={100}
      height={100}
      className={className}
    />
  );
}

export default Avatar;