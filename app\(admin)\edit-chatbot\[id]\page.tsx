'use client'; // Ensures client-side rendering

import { useEffect, useState, useCallback } from 'react';
import { usePara<PERSON>,useRouter } from 'next/navigation'; // Use this hook to get dynamic route params
import { createClient } from '../../../../supabaseClient';
import { Copy } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

import Avatar from '../../../../components/Avatar';
import { Button } from '../../../../components/ui/button';
import { Input } from '../../../../components/ui/input';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogFooter } from '../../../../components/ui/dialog'; // Custom modal for delete confirmation

// Type definitions
interface Characteristic {
  id: string;
  content: string;
}

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

function EditChatbot() {
  // Use the `useParams()` hook to get the dynamic `id` from the URL
  const { id } = useParams<{ id: string }>();
  const router = useRouter();

  const [url, setUrl] = useState('');
  const [newCharacteristic, setNewCharacteristic] = useState<string>('');
  const [chatbotName, setChatbotName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [chatbotCharacteristics, setChatbotCharacteristics] = useState<Characteristic[]>([]);
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [copied, setCopied] = useState(false); // Track if the URL has been copied

  // Memoize the fetchChatbot function to avoid unnecessary re-renders
  const fetchChatbot = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('chatbots')
        .select('name, chatbot_characteristics(content, id)')
        .eq('id', id)
        .single();

      if (error) throw error;

      if (data) {
        setChatbotName(data.name);
        setChatbotCharacteristics(data.chatbot_characteristics || []);
        setUrl(`${process.env.NEXT_PUBLIC_BASE_URL}/chatbot/${id}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [id]); // Add `id` as a dependency to refetch when it changes

  useEffect(() => {
    fetchChatbot();
  }, [fetchChatbot]); // Now `fetchChatbot` is a stable dependency

  useEffect(() => {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL?.replace(/\/$/, '') || window.location.origin;
    const chatbotUrl = `${baseUrl}/chatbot/${id}`.replace(/\/\//g, '/');
    setUrl(chatbotUrl);
  }, [id]);

  const handleAddCharacteristic = async (content: string) => {
    if (!content.trim()) return;

    try {
      const { error } = await supabase
        .from('chatbot_characteristics')
        .insert([{ chatbot_id: id, content }]);

      if (error) throw error;

      toast.success('Characteristic added');
      setNewCharacteristic('');
      setChatbotCharacteristics((prev) => [...prev, { id: Date.now().toString(), content }]); // Optimistic update
    } catch (err) {
      toast.error('Failed to add characteristic');

    }
  };

  const handleRemoveCharacteristic = async (characteristicId: string) => {
    try {
      const { error } = await supabase
        .from('chatbot_characteristics')
        .delete()
        .eq('id', characteristicId);

      if (error) throw error;

      toast.success('Characteristic removed successfully');
      setChatbotCharacteristics((prev) => prev.filter((char) => char.id !== characteristicId)); // Optimistic update
    } catch (err) {
      toast.error('Failed to remove characteristic');
   
    }
  };

  const handleUpdateChatbot = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    try {
      const { error } = await supabase
        .from('chatbots')
        .update({ name: chatbotName })
        .eq('id', id);

      if (error) throw error;

      toast.success('Chatbot updated successfully');
    } catch (err) {
      toast.error('Failed to update chatbot');
     
    }
  };

  const handleDelete = async () => {
    try {
      // First, let's log the chatbot ID we're trying to delete
  

      // Delete characteristics first
      const { error: characteristicsError } = await supabase
        .from('chatbot_characteristics')
        .delete()
        .eq('id', id);

      if (characteristicsError) {
      
      }

      // Delete the chatbot
      const { error: chatbotError } = await supabase
        .from('chatbots')
        .delete()
        .eq('id', id);

      if (chatbotError) throw chatbotError;

      toast.success('Chatbot deleted successfully');
      router.push('/'); // Adjust this to your chatbots listing page
      router.refresh();

    } catch (err) {

      toast.error('Failed to delete chatbot');
    } finally {
      setDeleteDialogOpen(false);
    }
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success('Copied to clipboard');
      setCopied(true);
      setTimeout(() => setCopied(false), 2000); // Reset the copied state after 2 seconds
    } catch (err) {
      toast.error('Failed to copy URL');
     
    }
  };

  if (loading) {
    return (
      <div className="mx-auto animate-spin p-10">
        <Avatar seed="Support Agent" />
      </div>
    );
  }

  if (error) {
    return <p className="text-red-500">Error: {error}</p>;
  }

  return (
    <div className="px-0 md:p-10">
      <div className="md:sticky md:top-0 z-50 sm:max-w-sm ml-auto space-y-2 md:border p-5 rounded-b-lg md:rounded-lg bg-[#2991EE]">
        <h2 className="text-white text-sm font-bold">Link to Chat</h2>
        <p className="text-sm italic text-white">
          Share this link with your customers to start conversations with your chatbot.
        </p>
        <div className="flex items-center space-x-2">
          <Link href={url} className="w-full cursor-pointer hover:opacity-50">
            <Input value={url} readOnly className="cursor-pointer" />
          </Link>
          <Button
            size="sm"
            className="px-3"
            onClick={handleCopyUrl}
          >
            <span className="sr-only">Copy</span>
            <Copy className="h-4 w-4" />
          </Button>
          {copied && <span className="text-green-500">Copied!</span>}
        </div>
      </div>

      <section className="relative mt-5 bg-white p-5 md:p-10 rounded-lg">
        <Button
          variant="destructive"
          className="absolute top-2 right-2 h-8 w-8"
          onClick={() => setDeleteDialogOpen(true)} // Open custom delete modal
        >
          X
        </Button>
        
        <div className="flex space-x-4">
          <Avatar seed={chatbotName} />
          <form onSubmit={handleUpdateChatbot} className="flex flex-1 space-x-2 items-center">
            <Input
              value={chatbotName}
              onChange={(e) => setChatbotName(e.target.value)}
              placeholder="Chatbot Name"
              className="w-full border-none bg-transparent text-xl font-bold"
              required
            />
            <Button type="submit" disabled={!chatbotName.trim()}>
              Update
            </Button>
          </form>
        </div>

        <h2 className="text-xl font-bold mt-10">Here&apos; what your AI knows...</h2>
        <p>
          Your chatbot is equipped with the following information to assist you in your
          conversations with your customers & users.
        </p>

        <div className="bg-gray-200 p-5 md:p-5 rounded-md mt-5">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleAddCharacteristic(newCharacteristic);
            }}
            className="flex space-x-2 mb-5"
          >
            <Input
              type="text"
              placeholder="Example: If customer asks for prices, provide pricing page: www.example.com/pricing"
              value={newCharacteristic}
              onChange={(e) => setNewCharacteristic(e.target.value)}
            />
            <Button type="submit" disabled={!newCharacteristic.trim()}>
              Add
            </Button>
          </form>

          <ul className="flex flex-wrap-reverse gap-5">
            {chatbotCharacteristics.map((characteristic) => (
              <li key={characteristic.id} className="bg-white p-3 rounded-md shadow flex justify-between items-center">
                {characteristic.content}
                <Button
                  variant="destructive"
                  onClick={() => handleRemoveCharacteristic(characteristic.id)}
                  className="ml-2 h-8 w-8"
                >
                  X
                </Button>
              </li>
            ))}
          </ul>
        </div>
      </section>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogTitle>Are you sure?</DialogTitle>
          <DialogDescription>
            Do you really want to delete this chatbot? This action cannot be undone.
          </DialogDescription>
          <DialogFooter>
            <Button variant="secondary" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default EditChatbot;
