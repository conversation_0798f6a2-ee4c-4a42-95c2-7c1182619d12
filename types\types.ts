export interface Chatbot {
    status: string;
    id: number;
    clerk_user_id: string;
    name: string;
    created_at: string;
    chatbot_characteristics: ChatbotCharacteristic[];
    chat_sessions: ChatSession[];
}

export interface ChatbotCharacteristic {
    id: number;
    chatbot_id: number; // Changed to number for consistency
    content: string;
    created_at: string;   
}

export interface Guest {
    id: number;
    name: string;
    email: string;
    created_at: string;   
}

export interface ChatSession {
    guest: any;
    status: string;
    email: string;
    name: string;
    user_id: string;
    id: number;
    chatbots: {
        length: number;
        name: string;
        guest?: any;
    }
    guest_id: number;
    created_at: string;   
    messages: Message[];
    guests: Guest;
}

export interface Message {
    sender: string;
    id: number;
    content: string | null ;
    created_at: string;
    chat_session_id: number;
    role: MessageRole;
} 

export interface GetChatbotByIdResponse {
    chatbots: {
        name: string;
        chatbot_characteristics: Array<{
            content: string;
        }>;
    };
}

export interface GetChatbotByIdVariables {
    id: string;
}

export interface GetChatbotsByUserData {
    chatbotsByUser: Chatbot[];
}

export interface GetChatbotsByUserDataVariables {
    clerk_user_id: string;
}

export interface GetUserChatbotsResponse {
    chatbotsByUser: Chatbot[];
}

export interface GetUserChatbotsVariables {
    userId: string;
}

export interface GetChatSessionMessagesResponse {
    chat_sessions: {
        id: number;
        created_at: string;
        messages: Message[];
        chatbots: {
            name: string;
        };
        guests: {
            name: string;
            email: string;
        }[];
    }; 
}

export interface GetChatSessionMessagesVariables {
    id: number;
}

export interface MessagesByChatSessionIdResponse {
    chat_sessions: {
        messages: Message[];
     } // This should contain your chat session details
}

export interface MessagesByChatSessionIdVariables {
    chat_sessions: any;
    chat_session_id: number; // Removed index signature for clarity
}

export interface ChatCompletionMessageParam {
    role: "user" | "system" | "ai" | "assistant"; // Adjust according to your use case
    name?: string;
    content: string;
}

export interface Database {
    public: {
      Tables: {
        chatbots: {
          Row: {
            id: string
            clerk_user_id: string
            name: string
            created_at: string
          }
          Insert: {
            clerk_user_id: string
            name: string
            created_at?: string
          }
          Update: {
            clerk_user_id?: string
            name?: string
            created_at?: string
          }
        }
        // Add other tables here
      }
    }
  }

export type MessageRole = "user" | "assistant" | "system";

export interface ChatCompletionMessage {
    role: MessageRole;
    content: string;
    name?: string;
}