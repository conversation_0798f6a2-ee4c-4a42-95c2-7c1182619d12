'use client';
import React, { useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { createClient } from '@supabase/supabase-js';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { useRouter } from 'next/navigation';

// Supabase client initialization (move to a separate utility file in production)
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

type BotCreationFormData = {
  botName: string;
  characteristics: string[];
};

const BotCreationForm: React.FC = () => {
  const router = useRouter();
  const { control, handleSubmit, register, formState: { errors } } = useForm<BotCreationFormData>();
  const [characteristics, setCharacteristics] = useState<string[]>([]);

  const addCharacteristic = (characteristic: string) => {
    if (characteristics.length < 5 && characteristic.trim()) {
      setCharacteristics([...characteristics, characteristic.trim()]);
    }
  };

  const removeCharacteristic = (index: number) => {
    const newCharacteristics = characteristics.filter((_, i) => i !== index);
    setCharacteristics(newCharacteristics);
  };

  const onSubmit = async (data: BotCreationFormData) => {
    try {
      // Generate a unique bot link
      const botLink = `/chatbot/${Math.random().toString(36).substring(7)}`;
      
      // Store bot data in Supabase
      const { data: botData, error } = await supabase
        .from('bots')
        .insert({
          name: data.botName,
          characteristics: characteristics,
          bot_link: botLink
        })
        .select()
        .single();

      if (error) throw error;

      // Redirect to the bot link page
      router.push(botLink);
    } catch (error) {
      console.error('Error creating bot:', error);
      // Handle error (show toast, error message, etc.)
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white shadow-lg rounded-xl">
      <h2 className="text-2xl font-bold mb-6 text-center">Create Your AI Bot</h2>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* Bot Name Input */}
        <div>
          <Controller
            name="botName"
            control={control}
            rules={{ 
              required: 'Bot name is required',
              minLength: { value: 3, message: 'Bot name must be at least 3 characters' }
            }}
            render={({ field }) => (
              <Input 
                {...field} 
                placeholder="Enter Bot Name"
                className="w-full"
              />
            )}
          />
          {errors.botName && (
            <p className="text-red-500 text-sm mt-1">
              {errors.botName.message}
            </p>
          )}
        </div>

        {/* Characteristics Section */}
        <div>
          <div className="flex">
            <Input 
              placeholder="Add Bot Characteristic"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addCharacteristic((e.target as HTMLInputElement).value);
                  (e.target as HTMLInputElement).value = '';
                }
              }}
              className="mr-2"
            />
            <Button 
              type="button"
              variant="outline"
              onClick={() => {
                const input = document.querySelector('input[placeholder="Add Bot Characteristic"]') as HTMLInputElement;
                addCharacteristic(input.value);
                input.value = '';
              }}
            >
              Add
            </Button>
          </div>
          
          {/* Characteristics List */}
          <div className="mt-4">
            {characteristics.map((char, index) => (
              <div 
                key={index} 
                className="flex justify-between items-center bg-gray-100 p-2 rounded mb-2"
              >
                <span>{char}</span>
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="sm"
                  onClick={() => removeCharacteristic(index)}
                  className="text-red-500"
                >
                  Remove
                </Button>
              </div>
            ))}
            {characteristics.length >= 5 && (
              <p className="text-sm text-gray-500">
                Maximum 5 characteristics allowed
              </p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <Button 
          type="submit" 
          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
        >
          Create Bot
        </Button>
      </form>
    </div>
  );
};

export default BotCreationForm;