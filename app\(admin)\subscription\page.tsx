'use client';

import { Suspense } from 'react';
import SubscriptionManager from '../../../components/SubscriptionManager';

export default function SubscriptionPage() {
  return (
    <div className="flex-1 p-10">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Subscription Management</h1>
          <p className="text-gray-600">
            Manage your SaffaBot subscription and billing preferences. Upgrade or downgrade your plan anytime.
          </p>
        </div>
        
        <Suspense fallback={
          <div className="space-y-8">
            <div className="bg-gray-100 rounded-lg p-8 animate-pulse">
              <div className="h-32 bg-gray-300 rounded"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="bg-gray-100 rounded-lg p-6 animate-pulse">
                  <div className="h-64 bg-gray-300 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        }>
          <SubscriptionManager />
        </Suspense>
      </div>
    </div>
  );
}
