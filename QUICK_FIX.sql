-- QUICK FIX: Just create the clients table to fix the immediate error
-- Copy and paste this into Supabase SQL Editor

CREATE TABLE IF NOT EXISTS clients (
    id SERIAL PRIMARY KEY,
    it_business_user_id VARCHAR(255) NOT NULL,
    client_name VARCHAR(255) NOT NULL,
    client_email VARCHAR(255),
    client_phone VARCHAR(50),
    client_company VARCHAR(255),
    brand_primary_color VARCHAR(7) DEFAULT '#4D7DFB',
    brand_secondary_color VARCHAR(7) DEFAULT '#FFCC00',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Add basic trigger for created_at
CREATE TRIGGER set_clients_created_at
BEFORE INSERT ON clients
FOR EACH ROW
EXECUTE FUNCTION set_created_at();
